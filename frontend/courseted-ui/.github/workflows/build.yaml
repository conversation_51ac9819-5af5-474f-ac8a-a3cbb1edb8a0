name: Build and Push to ECR

on:
  push:
    branches:
      - 'main'

jobs:
  build:
    name: Build and Push Image
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Generate unique image tag
        id: tag
        run: |
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          BRANCH_NAME=$(echo $BRANCH_NAME | sed 's/\//-/g')
          COMMIT_HASH=$(echo $GITHUB_SHA | head -c 7)
          echo "tag=${BRANCH_NAME}-${COMMIT_HASH}" >> $GITHUB_OUTPUT

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.prod
          push: true
          platforms: linux/amd64,linux/arm64
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/courseted-ui:${{ steps.tag.outputs.tag }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
