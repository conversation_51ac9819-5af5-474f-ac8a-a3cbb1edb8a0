{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitThis": false, "useUnknownInCatchVariables": false, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@theme/*": ["src/theme/*"], "@theme": ["src/theme"], "@app/*": ["src/app/*"], "@app": ["src/app"], "@routes/*": ["src/routes/*"], "@routes": ["src/routes"], "@components/*": ["src/components/*"], "@components": ["src/components"], "@features/*": ["src/features/*"], "@features": ["src/features"], "@api/*": ["src/api/*"], "@api": ["src/api"], "@utils/*": ["src/utils/*"], "@utils": ["src/utils"], "@hooks/*": ["src/hooks/*"], "@hooks": ["src/hooks"], "@pages/*": ["src/pages/*"], "@pages": ["src/pages"], "@assets/*": ["src/assets/*"], "@assets": ["src/assets"], "@types/*": ["src/types/*"], "@types": ["src/types"]}, "allowJs": true, "incremental": true, "esModuleInterop": true, "plugins": [{"name": "next"}]}, "include": ["src", "app", "pages", ".next/types/**/*.ts"], "references": [{"path": "./tsconfig.node.json"}], "exclude": ["node_modules"]}