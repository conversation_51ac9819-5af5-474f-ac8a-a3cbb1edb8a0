# Hydration Mismatch Prevention

This document explains how to prevent hydration mismatches in our Next.js application.

## What is a Hydration Mismatch?

A hydration mismatch occurs when the server-rendered HTML differs from what React renders on the client side during the initial render. This can cause:
- Console warnings
- Visual glitches
- Unexpected behavior
- Accessibility issues

## Solutions Implemented

### 1. Providers Component (`app/providers.tsx`)

The main providers component now includes hydration-safe rendering:

```tsx
"use client";

import { useState, useEffect } from 'react';
// ... other imports

export default function Providers({ children }: ProvidersProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div suppressHydrationWarning>
          {children}
        </div>
      </ThemeProvider>
    </Provider>
  );
}
```

### 2. ClientOnly Component (`app/components/ClientOnly.tsx`)

Use this component to wrap content that should only render on the client:

```tsx
import ClientOnly from '@/components/ClientOnly';

function MyComponent() {
  return (
    <div>
      <h1>This renders on both server and client</h1>
      <ClientOnly fallback={<div>Loading...</div>}>
        <div>This only renders on the client</div>
      </ClientOnly>
    </div>
  );
}
```

### 3. Custom Hooks (`src/hooks/useClient.ts`)

Two hooks are available for hydration-safe rendering:

#### `useIsClient()`
Returns `true` when the component has mounted on the client:

```tsx
import { useIsClient } from '@/hooks/useClient';

function MyComponent() {
  const isClient = useIsClient();

  return (
    <div>
      {isClient ? 'Client-side content' : 'Server-side content'}
    </div>
  );
}
```

#### `useHydrated()`
Returns `true` after hydration is complete:

```tsx
import { useHydrated } from '@/hooks/useClient';

function MyComponent() {
  const hydrated = useHydrated();

  if (!hydrated) {
    return <div>Loading...</div>;
  }

  return <div>Fully hydrated content</div>;
}
```

## Best Practices

1. **Use `suppressHydrationWarning` sparingly**: Only use it when you know the content will differ between server and client.

2. **Prefer conditional rendering**: Use hooks to conditionally render different content rather than completely different components.

3. **Graceful fallbacks**: Always provide meaningful fallback content for server-side rendering.

4. **Test thoroughly**: Test your components with JavaScript disabled to ensure they work properly during SSR.

## Common Scenarios

### Date/Time Display
```tsx
function CurrentTime() {
  const isClient = useIsClient();

  return (
    <div>
      {isClient ? new Date().toLocaleString() : 'Loading time...'}
    </div>
  );
}
```

### Theme Detection
```tsx
function ThemeToggle() {
  const [darkMode, setDarkMode] = useState(false);
  const hydrated = useHydrated();

  useEffect(() => {
    setDarkMode(localStorage.getItem('theme') === 'dark');
  }, []);

  if (!hydrated) {
    return <div>🌓</div>; // Neutral icon during SSR
  }

  return (
    <button onClick={() => setDarkMode(!darkMode)}>
      {darkMode ? '🌙' : '☀️'}
    </button>
  );
}
```

### Window-dependent Components
```tsx
function WindowSize() {
  const isClient = useIsClient();
  const [size, setSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    if (!isClient) return;

    function updateSize() {
      setSize({ width: window.innerWidth, height: window.innerHeight });
    }

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [isClient]);

  return (
    <div>
      {isClient ? `${size.width} x ${size.height}` : 'Calculating...'}
    </div>
  );
}
```

## Layout-specific Implementation

The `MainLayout` component has been updated to use hydration-safe patterns. The `suppressHydrationWarning` is applied to the main content area to prevent warnings while maintaining consistent rendering.

## Debugging Hydration Issues

1. Check the browser console for hydration warnings
2. Use React DevTools to inspect component state
3. Test with slow network connections to see the transition
4. Verify that fallback content is meaningful and accessible

Remember: The goal is to ensure that the initial server-rendered HTML is as close as possible to the first client render, while gracefully handling any differences.
