import React from 'react';
import {
  Input as BaseInput,
  InputAdornment,
  FormControl,
  FormHelperText,
  InputLabel,
  InputProps,
} from '@mui/material';

interface CustomInputProps extends InputProps {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  label: string;
  width?: string;
  height?: string;
  helperText?: string;
}

const Input = ({
  label,
  helperText,
  leftIcon,
  rightIcon,
  required,
  id,
  height,
  width,
  disabled,
  error,
  ...props
}: CustomInputProps) => {
  const generatedId = React.useId();
  const inputId = id || `text-input-${generatedId}`;
  const labelId = `${inputId}-label`;

  return (
    <FormControl
      fullWidth
      variant="standard"
      disabled={disabled}
      required={required}
      error={error}
      sx={{
        width: width || '100%',
        '& label': {
          color: 'grey.900 !important',
        },
      }}
    >
      {label && (
        <InputLabel
          htmlFor={inputId}
          id={labelId}
          shrink
          margin="dense"
          sx={{
            color: 'grey.900',
            fontSize: '18px',
            lineHeight: '20px',
            fontWeight: 500,
            '&.Mui-disabled': {
              color: 'grey.900',
            },
            '&.Mui-focused': {
              color: 'grey.900',
            },
            '&.Mui-error': {
              color: 'red.500',
            },
          }}
        >
          {label}
        </InputLabel>
      )}
      <BaseInput
        id={inputId}
        disableUnderline
        startAdornment={
          leftIcon && (
            <InputAdornment
              position="start"
              sx={{
                height: '100%',
                alignItems: 'center',
                display: 'flex',
              }}
            >
              {leftIcon}
            </InputAdornment>
          )
        }
        endAdornment={
          rightIcon && (
            <InputAdornment
              position="end"
              sx={{
                height: '100%',
                alignItems: 'center',
                display: 'flex',
              }}
            >
              {rightIcon}
            </InputAdornment>
          )
        }
        sx={{
          ...props.sx,
          border: '1px solid',
          borderColor: disabled ? 'grey.100' : 'grey.300',
          borderRadius: '10px',
          height: height || '44px',
          px: 0,
          py: 0,
          pr: rightIcon ? 1.5 : 0,
          pl: leftIcon ? 1.5 : 0,
          backgroundColor: disabled ? 'grey.100' : 'inherit',
          '&:hover': {
            borderColor: disabled ? 'grey.100' : 'grey.300',
          },
          '&.Mui-focused': {
            borderColor: error ? 'red.500' : 'green.600',
            borderWidth: '2px',
          },
          input: {
            padding:
              leftIcon && rightIcon
                ? '8px 0 8px 0'
                : leftIcon
                  ? '8px 12px 8px 0'
                  : rightIcon
                    ? '8px 0 8px 12px'
                    : '8px 12px',
            color: disabled ? 'grey.500' : 'grey.900',
            fontSize: '14px',
            lineHeight: '20px',
            fontWeight: 400,
            '&::placeholder': {
              color: disabled ? 'grey.400' : 'grey.600',
              opacity: 1,
            },
          },
        }}
        {...props}
      />
      {helperText && (
        <FormHelperText
          sx={{
            color: error ? 'red.500' : 'grey.600',
            fontSize: '12px',
            fontWeight: 400,
            marginLeft: 0,
          }}
        >
          {helperText}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export default Input;
