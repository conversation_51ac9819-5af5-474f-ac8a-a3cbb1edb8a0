import React, { useState } from 'react';
import {
  Box,
  TextField,
  InputLabel,
  Grid,
  Typography,
  // SelectChangeEvent,
  Autocomplete,
} from '@mui/material';
import { PhotoCamera } from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import Icon from '@/components/Icon';
import Input from '@/components/Input';
import Button from '@/components/Button';
import { useGetCountriesQuery } from '@/features/countries/countriesApi';
import { Country } from '@/types/countries.types';

const ProfilePhotoSection = styled(Box)(({ theme }) => ({
  display: 'flex',
  // flexDirection: 'column',
  alignItems: 'center',
  marginBottom: theme.spacing(3),
}));

interface StudentInformationData {
  profilePhoto: File | null;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  selectedCountry: Country | null;
}

interface StudentInformationStepProps {
  data: StudentInformationData;
  onDataChange: (data: StudentInformationData) => void;
  onNext: () => void;
}

export const StudentInformationStep: React.FC<StudentInformationStepProps> = ({
  data,
  onDataChange,
  onNext,
}) => {
  const [profilePhotoPreview, setProfilePhotoPreview] = useState<string | null>(null);

  const { data: countriesResponse, isLoading: isCountriesLoading } = useGetCountriesQuery();
  const countries: Country[] = countriesResponse?.data || [];

  const handleInputChange = (field: keyof StudentInformationData) => (
    event: React.ChangeEvent<HTMLInputElement | { value: unknown }>
  ) => {
    onDataChange({
      ...data,
      [field]: event.target.value,
    });
  };

  // const handleSelectChange = (field: keyof StudentInformationData) => (
  //   event: SelectChangeEvent<string>
  // ) => {
  //   onDataChange({
  //     ...data,
  //     [field]: event.target.value,
  //   });
  // };

  const handleCountryChange = async (event: any, newValue: Country | null) => {
    const newFormData = {
      ...data,
      selectedCountry: newValue,
    };
    onDataChange(newFormData);

    // setFormData(newFormData);

    // // Validate country field
    // await validateField('selectedCountry', newValue, newFormData);
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onDataChange({
        ...data,
        profilePhoto: file,
      });

      const reader = new FileReader();
      reader.onload = (e) => {
        setProfilePhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const isFormValid = () => {
    return (
      data.firstName.trim() !== '' &&
      data.lastName.trim() !== '' &&
      data.phoneNumber.trim() !== '' &&
      data.selectedCountry !== null
    );
  };



  const UploadButton = ({ children }) => (
    <Box
      display="flex"
      alignItems="center"
      justifyContent="center"
      width={40}
      height={40}
      borderRadius="50%"
      bgcolor="#f0f0f0"
    >
      {children}
    </Box>
  );

  const UploadBox = ({ handlePhotoUpload, profilePhotoPreview }) => {
    return (
      <label htmlFor="profile-photo-upload" style={{ cursor: 'pointer' }}>
        <input
          accept="image/*"
          style={{ display: 'none' }}
          id="profile-photo-upload"
          type="file"
          onChange={handlePhotoUpload}
        />
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          border="1px dashed #ccc"
          borderRadius={2}
          height={160}
          width={160}
          mb={3}
          p={2}
          position="relative"
          sx={{ '&:hover': { borderColor: 'primary.main' } }}
        >
          {/* Preview Avatar or Upload Icon */}
          {profilePhotoPreview ? (
            <img
              src={profilePhotoPreview}
              alt="Profile"
              style={{ width: '100%', height: '100%', borderRadius: 8, objectFit: 'cover' }}
            />
          ) : (
            <UploadButton>
              <PhotoCamera sx={{ fontSize: 24 }} />
            </UploadButton>
          )}
          <Box mt={1} fontSize={12} color="text.secondary">
            Upload profile photo
          </Box>
        </Box>
      </label>
    );
  };

  return (
    <Box>
      <ProfilePhotoSection>
        <UploadBox
          profilePhotoPreview={profilePhotoPreview} // optional
          handlePhotoUpload={handlePhotoUpload}
        />

      </ProfilePhotoSection>

      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <Input
            label="First name"
            placeholder="Enter first name"
            fullWidth
            id="firstName"
            name="firstName"
            type="text"
            value={data.firstName}
            onChange={handleInputChange('firstName')}
            required
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Input
            label="Last name"
            placeholder="Enter last name"
            fullWidth
            id="lastName"
            name="lastName"
            type="text"
            value={data.lastName}
            onChange={handleInputChange('lastName')}
            required
          />
        </Grid>
        <Grid item xs={12}>
          <Input
            label="Phone number"
            placeholder="Enter phone number"
            fullWidth
            id="phoneNumber"
            name="phoneNumber"
            type="text"
            value={data.phoneNumber}
            onChange={handleInputChange('phoneNumber')}
            required
          />
        </Grid>
        <Grid item xs={12}>
          <Box>
            <InputLabel
              htmlFor={'selectedCountry'}
              id={'selectedCountry-label'}
              shrink
              margin="dense"
              sx={{
                color: 'grey.900',
                fontSize: '18px',
                lineHeight: '20px',
                fontWeight: 500,
                '&.Mui-disabled': {
                  color: 'grey.900',
                },
                '&.Mui-focused': {
                  color: 'grey.900',
                },
                '&.Mui-error': {
                  color: 'red.500',
                },
              }}
            >
              Country
            </InputLabel>
            <Autocomplete
              options={countries}
              getOptionLabel={(option) => option.name}
              value={data.selectedCountry}
              onChange={handleCountryChange}
              loading={isCountriesLoading}
              // disabled={isRegistering}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder="Search and select your country"
                />
              )}
              renderOption={(props, option) => (
                <Box component="li" {...props} key={option.id} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography sx={{ fontSize: '1.2rem' }}>{option.flag}</Typography>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="body2">{option.name}</Typography>
                  </Box>
                  <Typography variant="caption" color="text.secondary">
                    {option.phoneCode}
                  </Typography>
                </Box>
              )}
              filterOptions={(options, { inputValue }) => {
                return options.filter(
                  (option) =>
                    option.name.toLowerCase().includes(inputValue.toLowerCase()) ||
                    option.code.toLowerCase().includes(inputValue.toLowerCase()) ||
                    option.phoneCode.includes(inputValue)
                );
              }}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              sx={{
                '&:hover': {
                  // borderColor: 'grey.300',
                },
                '&.Mui-focused': {
                  // borderColor: 'green.600',
                  // borderWidth: '2px',
                },
                '& fieldset': {
                  height: '50px',
                  padding: '0 12px',
                  borderRadius: '10px',
                  borderColor: 'grey.300',
                },
                "& .MuiAutocomplete-inputRoot": {
                  padding: '4px 12px',
                  fontSize: '14px',
                },
              }}
            />
          </Box>
        </Grid>
      </Grid>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 4 }}>
        <Button
          type="submit"
          variant="primary"
          size="md"
          // fullWidth
          disabled={!isFormValid()}
          onClick={onNext}
          sx={{ px: 4 }}
        >
          Next  <Icon name="ArrowRight" sx={{ ml: 1 }} />
        </Button>
      </Box>
    </Box>
  );
};
