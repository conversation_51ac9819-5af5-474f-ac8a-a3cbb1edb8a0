import React from 'react';
import {
  Box,
  Grid,
  SelectChangeEvent,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Icon from '../Icon';
import Input from '../Input';
import Select from '../Select';
import Button from '../Button';

const ButtonGroup = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  marginTop: theme.spacing(4),
}));

const PreviousButton = styled(Button)(() => ({
  backgroundColor: 'transparent',
  color: '#666',
  borderRadius: 32,
  padding: '12px 24px',
  textTransform: 'none',
  fontSize: '14px',
  fontWeight: 500,
  border: '1px solid #e0e0e0',
  '&:hover': {
    backgroundColor: '#f5f5f5',
    borderColor: '#ccc',
  },

}));

interface EducationSkillsData {
  linkedinUrl: string;
  githubUrl: string;
  educationLevel: string;
  major: string;
}

interface EducationSkillsStepProps {
  data: EducationSkillsData;
  onDataChange: (data: EducationSkillsData) => void;
  onNext: () => void;
  onPrevious: () => void;
}

const educationLevels = [
  'High School',
  'Associate Degree',
  'Bachelor\'s Degree',
  'Master\'s Degree',
  'Doctoral Degree',
  'Professional Degree',
  'Certificate Program',
  'Other',
];

export const EducationSkillsStep: React.FC<EducationSkillsStepProps> = ({
  data,
  onDataChange,
  onNext,
  onPrevious,
}) => {
  const handleInputChange = (field: keyof EducationSkillsData) => (
    event: React.ChangeEvent<HTMLInputElement | { value: unknown }>
  ) => {
    onDataChange({
      ...data,
      [field]: event.target.value,
    });
  };

  const handleSelectChange = (field: keyof EducationSkillsData) => (
    event: SelectChangeEvent<string>
  ) => {
    onDataChange({
      ...data,
      [field]: event.target.value,
    });
  };

  const isFormValid = () => {
    return (
      data.linkedinUrl.trim() !== ''
      && data.githubUrl.trim() !== ''
      // && data.educationLevel !== ''
      // && data.major.trim() !== ''
    );
  };

  const validateUrl = (url: string, platform: string) => {
    if (!url) return true; // Allow empty URLs

    const patterns = {
      linkedin: /^https?:\/\/(www\.)?linkedin\.com\/in\/[a-zA-Z0-9-]+\/?$/,
      github: /^https?:\/\/(www\.)?github\.com\/[a-zA-Z0-9-]+\/?$/,
    };

    return patterns[platform as keyof typeof patterns]?.test(url) || false;
  };

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          {/* <StyledTextField
            fullWidth
            label="LinkedIn URL*"
            value={data.linkedinUrl}
            onChange={handleInputChange('linkedinUrl')}
            variant="outlined"
            size="medium"
            placeholder="https://linkedin.com/in/your-profile"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <LinkedIn sx={{ color: '#0077b5', fontSize: 20 }} />
                </InputAdornment>
              ),
            }}
            error={data.linkedinUrl !== '' && !validateUrl(data.linkedinUrl, 'linkedin')}
            helperText={
              data.linkedinUrl !== '' && !validateUrl(data.linkedinUrl, 'linkedin')
                ? 'Please enter a valid LinkedIn URL'
                : ''
            }
          /> */}
          <Input
            label="LinkedIn URL"
            value={data.linkedinUrl}
            onChange={handleInputChange('linkedinUrl')}
            placeholder="https://linkedin.com/in/your-profile"
            fullWidth
            id="linkedinUrl"
            name="linkedinUrl"
            type="text"
            required
            error={data.linkedinUrl !== '' && !validateUrl(data.linkedinUrl, 'linkedin')}
            helperText={
              data.linkedinUrl !== '' && !validateUrl(data.linkedinUrl, 'linkedin')
                ? 'Please enter a valid LinkedIn URL'
                : ''
            }
          // startAdornment={
          //   <InputAdornment position="start">
          //     <LinkedIn sx={{ color: '#0077b5', fontSize: 20 }} />
          //   </InputAdornment>
          // }
          // error={!!errors.email}
          // helperText={errors.email}
          // disabled={isLoggingIn}
          />
        </Grid>

        <Grid item xs={12}>
          {/* <StyledTextField
            fullWidth
            label="GitHub URL*"
            value={data.githubUrl}
            onChange={handleInputChange('githubUrl')}
            variant="outlined"
            size="medium"
            placeholder="https://github.com/your-username"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <GitHub sx={{ color: '#333', fontSize: 20 }} />
                </InputAdornment>
              ),
            }}
            error={data.githubUrl !== '' && !validateUrl(data.githubUrl, 'github')}
            helperText={
              data.githubUrl !== '' && !validateUrl(data.githubUrl, 'github')
                ? 'Please enter a valid GitHub URL'
                : ''
            }
          /> */}
          <Input
            label="GitHub URL"
            value={data.githubUrl}
            onChange={handleInputChange('githubUrl')}
            placeholder="https://github.com/your-username"
            fullWidth
            id="githubUrl"
            name="githubUrl"
            type="text"
            required
            error={data.githubUrl !== '' && !validateUrl(data.githubUrl, 'github')}
            helperText={
              data.githubUrl !== '' && !validateUrl(data.githubUrl, 'github')
                ? 'Please enter a valid GitHub URL'
                : ''
            }
          />
        </Grid>

        <Grid item xs={12}>
          <Select
            label="Education Level"
            // placeholder="Select your education level"
            value={data.educationLevel}
            // onChange={() => handleSelectChange('educationLevel')}
            onChange={handleSelectChange('educationLevel')}
            options={educationLevels.map((level) => ({ label: level, value: level }))}
            // leftIcon={<LocationOn />}
            // helperText="Please select your country"
            // error={false}
            required
            renderValue={(selected) => {
              if (selected.length === 0) {
                return <>Select your education level</>;
              }
              return selected;
            }}
          />
        </Grid>

        <Grid item xs={12}>
          {/* <StyledTextField
            fullWidth
            label="What's your major?*"
            value={data.major}
            onChange={handleInputChange('major')}
            variant="outlined"
            size="medium"
            placeholder="e.g., Computer Science, Business Administration"
          /> */}
          <Input
            fullWidth
            label="What's your major?"
            value={data.major}
            onChange={handleInputChange('major')}
            placeholder="e.g., Computer Science, Business Administration"
            id="major"
            name="major"
            type="text"
          // required
          // error={data.major !== '' && !validateUrl(data.major, 'major')}
          // helperText={
          //   data.major !== '' && !validateUrl(data.major, 'major')
          //     ? 'Please enter a valid major subject name'
          //     : ''
          // }
          />
        </Grid>
      </Grid>

      <ButtonGroup>
        <PreviousButton onClick={onPrevious}>
          <Icon name="ArrowLeft" sx={{ mr: 1 }} /> Previous
        </PreviousButton>

        <Button
          type="submit"
          variant="primary"
          size="md"
          // fullWidth
          disabled={!isFormValid() ||
            (data.linkedinUrl !== '' && !validateUrl(data.linkedinUrl, 'linkedin')) ||
            (data.githubUrl !== '' && !validateUrl(data.githubUrl, 'github'))
          }
          onClick={onNext}
          sx={{ px: 4 }}
        // loading={isLoggingIn}
        >
          Next  <Icon name="ArrowRight" sx={{ ml: 1 }} />
        </Button>
      </ButtonGroup>
    </Box>
  );
};
