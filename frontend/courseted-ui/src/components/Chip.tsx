import theme from '@/theme';
import { Chip as BaseChip, ChipProps } from '@mui/material';
import Icon from '@components/Icon';
interface CustomChipProps extends ChipProps {
  bgColor?: string;
  textColor?: string;
  customDeleteIcon?: React.ReactElement;
}
const Chip = ({ bgColor, textColor, customDeleteIcon, ...props }: CustomChipProps) => {
  const background = bgColor || theme.palette.grey[200];
  const color = textColor || theme.palette.grey[900];
  return (
    <BaseChip
      sx={{ background, color, ...props.sx }}
      {...props}
      deleteIcon={customDeleteIcon || <Icon name="CrossIcon" sx={{ mx: 1 }} />}
    />
  );
};

export default Chip;
