import theme from '@/theme';
import { But<PERSON> as BaseButton, ButtonProps, CircularProgress, SxProps, Theme } from '@mui/material';
import React, { useMemo } from 'react';

type ButtonVariant = 'primary' | 'outlined' | 'text';
type ButtonSize = 'xl' | 'lg' | 'md' | 'sm';

interface CustomButtonProps extends Omit<ButtonProps, 'variant' | 'size'> {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loading?: boolean;
  children: React.ReactNode;
  variant?: ButtonVariant;
  size?: ButtonSize;
}

const grey = theme.palette.grey;

const baseStyles = {
  borderRadius: '100px',
  fontWeight: 500,
  transition: 'all 0.2s ease',
  textTransform: 'none',
};

const variantStyles = {
  primary: {
    backgroundColor: grey[900],
    color: 'white',
    border: 'none',
    '&:hover': { backgroundColor: grey[800] },
    '&:disabled': { backgroundColor: grey[300], color: grey[500] },
  },
  outlined: {
    backgroundColor: 'white',
    color: grey[900],
    border: `1px solid ${grey[300]}`,
    '&:hover': { backgroundColor: grey[100], color: grey[800] },
    '&:disabled': { backgroundColor: 'white', color: grey[500] },
  },
  text: {
    backgroundColor: 'transparent',
    color: grey[900],
    border: 'none',
    '&:hover': { backgroundColor: grey[100], color: grey[800] },
    '&:disabled': { color: grey[500] },
  },
};

const sizeStyles = {
  xl: { fontSize: '16px', padding: '16px 24px', height: '56px' },
  lg: { fontSize: '14px', padding: '12px 16px', height: '44px' },
  md: { fontSize: '14px', padding: '10px 12px', height: '40px' },
  sm: { fontSize: '14px', padding: '6px 12px', height: '32px' },
};

const Button: React.FC<CustomButtonProps> = ({
  leftIcon,
  rightIcon,
  loading = false,
  children,
  disabled = false,
  variant = 'primary',
  size = 'lg',
  sx,
  ...props
}) => {
  const componentStyles = useMemo(
    () => ({
      ...baseStyles,
      ...variantStyles[variant],
      ...sizeStyles[size],
    }),
    [variant, size]
  );

  return (
    <BaseButton
      disabled={disabled || loading}
      startIcon={!loading && leftIcon}
      endIcon={!loading && rightIcon}
      sx={{ ...componentStyles, ...sx } as SxProps<Theme>}
      {...props}
    >
      {loading ? <CircularProgress size={20} color="inherit" /> : children}
    </BaseButton>
  );
};

export default Button;
