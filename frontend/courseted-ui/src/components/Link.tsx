import theme from '@/theme';
import { palette } from '@/theme/palette';
import { Link as BaseLink, LinkProps } from '@mui/material';

export type LinkSize = 'lg' | 'md';
export type LinkType = 'primary' | 'secondary';
export type LinkState = 'focus' | 'hover' | 'default' | 'disabled';

interface CustomLinkProps extends LinkProps {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  customSize?: LinkSize;
  customType?: LinkType;
}

const Link = ({
  leftIcon,
  rightIcon,
  children,
  disabled = false,
  customSize = 'lg',
  customType = 'primary',
  ...props
}: CustomLinkProps) => {
  const { textColor } = linkType(customType);

  return (
    <BaseLink
      underline="none"
      sx={{
        width: 'auto',
        height: 'auto',
        pointerEvents: disabled ? 'none' : 'auto',
        '&:hover': {
          color: linkState('hover', customType).textColor,
        },
        '&[aria-disabled="true"]': {
          color: linkState('disabled', customType).textColor,
        },
        ...props.sx,
      }}
      variant={customSize === 'lg' ? 'textMd' : 'textSm'}
      color={textColor}
      aria-disabled={disabled}
      tabIndex={disabled ? -1 : 0}
      onClick={disabled ? e => e.preventDefault() : undefined}
      {...props}
    >
      {leftIcon && <span style={{ marginRight: 8 }}>{leftIcon}</span>}
      {children}
      {rightIcon && <span style={{ marginLeft: 8 }}>{rightIcon}</span>}
    </BaseLink>
  );
};

function linkType(type: LinkType) {
  const base: { textColor: string } = {
    textColor: theme.palette.primary[600],
  };

  switch (type) {
    case 'primary':
      return base;
    case 'secondary':
      return {
        textColor: theme.palette.grey[900],
      };
  }
}

function linkState(state: LinkState, type: LinkType) {
  const grey = theme.palette.grey;
  const primary = palette.primary;

  if (type === 'primary') {
    switch (state) {
      case 'disabled':
        return { textColor: grey[500] };
      case 'hover':
        return { textColor: primary[800] };
      default:
        return { textColor: primary[600] };
    }
  }

  switch (state) {
    case 'disabled':
      return { textColor: grey[500] };
    case 'hover':
      return { textColor: grey[700] };
    default:
      return { textColor: grey[900] };
  }
}

export default Link;
