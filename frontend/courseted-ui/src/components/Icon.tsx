import { SxProps } from '@mui/system';
import { Box } from '@mui/material';
import React from 'react';

export type IconName = keyof typeof IconComponentMappings;

interface IconProps extends React.SVGAttributes<SVGElement> {
  name: IconName;
  className?: string;
  sx?: SxProps;
}

const IconComponentMappings = {
  ArrayUpRight: ArrayUpRight,
  Lesson,
  Clock,
  Students,
  BookmarkAdd2,
  CtaArrow,
  Facebook,
  TwitterX,
  Instagram,
  LinkedIn,
  Youtube,
  LogoWhite,
  LogoBlack,
  ArrowRightGreen,
  RatingStar,
  VideoCameraIconLanding,
  SprayedDesignLanding,
  HeroStarIcon,
  HeroStarIconSmall,
  HeroStarIconBig,
  ArrowRight,
  ArrowLeft,
  CrossIcon,
  Tick_02,
  Remove_01,
  ArrowUpRight01,
  InputCircleTick,
  ArrowDown,
  ViewOn,
  ViewOff,
  LinkedInColored,
  Google,
  SquareLock,
  Mail2,
  Search,
  ShoppingCart,
  ArrowRight2,
  Menu,
};

export default function Icon({ name, className = '', sx, ...others }: IconProps) {
  const Component = IconComponentMappings[name];

  if (!Component) return null;

  return (
    <Box component="span" sx={sx}>
      <Component className={className} {...others} />
    </Box>
  );
}

// Define the actual SVG icon
function LogoWhite(props: React.SVGAttributes<SVGElement>) {
  return (
    <svg
      width="614"
      height="100"
      viewBox="0 0 614 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M52.7488 0.00189576C27.9625 0.00189576 8.08718 20.3406 12.9793 46.7664C19.0911 69.6297 35.3887 74.0501 41.9381 75.1902C33.5913 70.7926 28.2958 61.4613 27.8337 50.6811C27.1234 34.0639 37.0971 23.4864 50.8568 23.4864C60.1504 23.4864 68.2566 29.0412 72.5351 37.276H97.5393C92.1566 15.7895 74.1431 0 52.7488 0V0.00189576Z"
        fill="white"
      />
      <path
        d="M63.48 62.9934L69.9593 67.3854L72.0862 68.8437C68.7282 73.0615 59.7963 86.4287 36.5099 80.4724C26.1253 77.8152 15.0096 71.4441 8.05877 57.6412C5.62881 52.8193 3.70075 47.1243 2.47346 40.4407C2.47346 40.4407 1.02458 79.622 33.0762 95.5384C57.1258 107.474 78.3894 93.0972 88.5543 80.139L97.0752 86.3908L93.0979 55.986L63.4819 62.9934H63.48Z"
        fill="#24BA5E"
      />
      <path
        d="M138.955 93.6326C132.713 93.6326 127.111 92.2708 122.154 89.5474C117.194 86.8239 113.283 82.9716 110.42 77.9924C107.554 73.0133 106.123 67.161 106.123 60.4356C106.123 53.7102 107.619 47.625 110.613 42.6875C113.605 37.7519 117.623 33.9413 122.669 31.2595C127.714 28.5777 133.314 27.2368 139.471 27.2368C145.628 27.2368 151.334 28.5777 156.335 31.2595C161.336 33.9413 165.312 37.7519 168.263 42.6875C171.213 47.625 172.688 53.5398 172.688 60.4356C172.688 66.8201 171.255 72.5227 168.391 77.5454C165.526 82.5682 161.55 86.5057 156.464 89.3561C151.376 92.2064 145.541 93.6326 138.957 93.6326H138.955ZM139.084 81.1193C142.759 81.1193 145.988 80.2064 148.768 78.375C151.546 76.5454 153.748 74.0549 155.372 70.9053C156.996 67.7576 157.809 64.1818 157.809 60.1799C157.809 56.178 156.996 52.392 155.372 49.3276C153.746 46.2632 151.544 43.9015 148.768 42.2405C145.988 40.5814 142.804 39.75 139.213 39.75C135.622 39.75 132.179 40.6439 129.402 42.4318C126.622 44.2197 124.507 46.6667 123.054 49.7727C121.599 52.8807 120.873 56.4337 120.873 60.4337C120.873 64.7746 121.707 68.5 123.374 71.606C125.042 74.714 127.263 77.0758 130.043 78.6913C132.821 80.3087 135.834 81.1174 139.084 81.1174V81.1193Z"
        fill="white"
      />
      <path
        d="M208.339 93.6326C203.037 93.6326 198.483 92.5246 194.68 90.3125C190.874 88.1004 187.947 84.9299 185.894 80.8011C183.842 76.6742 182.817 71.7992 182.817 66.1818V28.7708H197.308V64.1383C197.308 69.4167 198.568 73.608 201.093 76.714C203.613 79.822 207.356 81.375 212.315 81.375C217.275 81.375 221.292 79.7595 224.114 76.5227C226.936 73.2898 228.345 68.8201 228.345 63.1155V28.7689H242.837V92.0985H228.73L228.474 83.4167C226.337 86.6534 223.473 89.1629 219.882 90.9508C216.291 92.7386 212.444 93.6326 208.339 93.6326Z"
        fill="white"
      />
      <path
        d="M256.432 92.1004V28.7708H270.539L270.795 39.1136C272.931 35.3693 275.817 32.4545 279.452 30.3674C283.084 28.2822 287.295 27.2386 292.084 27.2386V42.4318H288.108C282.292 42.4318 277.977 43.8807 275.155 46.7727C272.334 49.6667 270.924 54.178 270.924 60.3068V92.1004H256.43H256.432Z"
        fill="white"
      />
      <path
        d="M326.714 93.6326C321.84 93.6326 317.33 92.7595 313.184 91.0152C309.035 89.2708 305.658 86.7822 303.051 83.5454C300.441 80.3125 298.967 76.5227 298.626 72.1818H313.246C313.76 75.1629 315.234 77.5663 317.671 79.3958C320.108 81.2273 323.078 82.1401 326.585 82.1401C329.748 82.1401 332.377 81.4602 334.473 80.0966C336.567 78.7367 337.615 76.8617 337.615 74.4792C337.615 72.4356 336.887 70.8409 335.434 69.6913C333.978 68.5417 332.184 67.6477 330.048 67.0095C327.91 66.3712 325.687 65.7973 323.379 65.286C319.36 64.3504 315.621 63.2008 312.156 61.839C308.694 60.4792 305.891 58.5625 303.756 56.0928C301.618 53.625 300.549 50.3902 300.549 46.3883C300.549 42.3864 301.66 38.9621 303.885 36.1099C306.107 33.2595 309.079 31.0663 312.799 29.5341C316.519 28.0019 320.686 27.2368 325.304 27.2368C332.314 27.2368 338.149 28.9829 342.811 32.4716C347.471 35.9621 350.058 41.0701 350.569 47.7936H336.718C336.375 44.9015 335.157 42.6856 333.063 41.1534C330.967 39.6212 328.253 38.8561 324.92 38.8561C322.013 38.8561 319.597 39.411 317.673 40.5152C315.75 41.6231 314.787 43.4091 314.787 45.8769C314.787 47.5814 315.492 48.9848 316.904 50.0909C318.314 51.1989 320.11 52.1136 322.289 52.8352C324.471 53.5587 326.627 54.1761 328.767 54.6856C333.127 55.7064 337.018 56.8996 340.438 58.2614C343.857 59.6231 346.573 61.5398 348.581 64.0076C350.59 66.4773 351.638 69.839 351.723 74.0947C351.723 78.1799 350.611 81.6723 348.388 84.5644C346.163 87.4583 343.171 89.6932 339.411 91.267C335.648 92.8409 331.416 93.6288 326.714 93.6288V93.6326Z"
        fill="white"
      />
      <path
        d="M392.89 93.6326C386.477 93.6326 380.877 92.2064 376.088 89.3561C371.299 86.5057 367.559 82.589 364.866 77.6098C362.173 72.6307 360.825 66.9072 360.825 60.4375C360.825 53.9678 362.173 47.8826 364.866 42.9451C367.559 38.0095 371.32 34.1572 376.153 31.3901C380.981 28.625 386.606 27.2405 393.017 27.2405C399.428 27.2405 404.689 28.5814 409.305 31.2632C413.921 33.9451 417.556 37.6269 420.207 42.3068C422.857 46.9886 424.183 52.3106 424.183 58.267C424.183 59.4602 424.16 60.5227 424.119 61.4583C424.075 62.3958 423.969 63.4167 423.798 64.5227H375.448C375.704 67.8428 376.558 70.8011 378.012 73.3958C379.465 75.9943 381.453 78.0152 383.976 79.4602C386.496 80.9091 389.382 81.6307 392.632 81.6307C396.136 81.6307 399.109 80.8636 401.547 79.3333C403.984 77.8011 405.884 75.589 407.254 72.6932H422.517C421.32 76.5227 419.417 80.0151 416.809 83.1629C414.2 86.3125 410.91 88.8447 406.934 90.7595C402.958 92.6742 398.278 93.6326 392.89 93.6326ZM375.577 54.4337H409.307C409.222 49.7538 407.554 46.0511 404.306 43.3258C401.056 40.6023 397.165 39.2405 392.634 39.2405C388.529 39.2405 384.83 40.4754 381.54 42.9432C378.249 45.4129 376.261 49.2424 375.577 54.4337Z"
        fill="white"
      />
      <path
        d="M458.171 92.1004C451.673 92.1004 446.713 90.5265 443.293 87.3769C439.872 84.2292 438.163 79.2046 438.163 72.3106V40.9015H429.057V28.7727H438.163V13.4508H452.783V28.7727H468.302V40.9015H452.783V72.4394C452.783 75.3333 453.467 77.3125 454.836 78.3769C456.202 79.4413 458.254 79.9735 460.993 79.9735H468.304V92.1023H458.171V92.1004Z"
        fill="white"
      />
      <path
        d="M507.806 93.6326C501.394 93.6326 495.794 92.2064 491.005 89.3561C486.216 86.5057 482.475 82.589 479.782 77.6098C477.089 72.6307 475.742 66.9072 475.742 60.4375C475.742 53.9678 477.089 47.8826 479.782 42.9451C482.475 38.0095 486.237 34.1572 491.069 31.3901C495.898 28.625 501.522 27.2405 507.933 27.2405C514.344 27.2405 519.605 28.5814 524.221 31.2632C528.838 33.9451 532.473 37.6269 535.124 42.3068C537.773 46.9886 539.1 52.3106 539.1 58.267C539.1 59.4602 539.077 60.5227 539.035 61.4583C538.992 62.3958 538.885 63.4167 538.715 64.5227H490.364C490.62 67.8428 491.475 70.8011 492.928 73.3958C494.382 75.9943 496.37 78.0152 498.892 79.4602C501.413 80.9091 504.299 81.6307 507.549 81.6307C511.053 81.6307 514.026 80.8636 516.463 79.3333C518.9 77.8011 520.801 75.589 522.171 72.6932H537.434C536.236 76.5227 534.334 80.0151 531.726 83.1629C529.116 86.3125 525.827 88.8447 521.851 90.7595C517.875 92.6742 513.194 93.6326 507.806 93.6326ZM490.491 54.4337H524.221C524.136 49.7538 522.469 46.0511 519.22 43.3258C515.97 40.6023 512.08 39.2405 507.549 39.2405C503.444 39.2405 499.745 40.4754 496.455 42.9432C493.163 45.4129 491.175 49.2424 490.491 54.4337Z"
        fill="white"
      />
      <path
        d="M577.575 93.6326C571.247 93.6326 565.841 92.1193 561.351 89.1004C556.862 86.0795 553.4 82.0587 550.963 77.0341C548.526 72.0133 547.307 66.3939 547.307 60.1799C547.307 53.9659 548.526 48.4962 550.963 43.517C553.4 38.5379 556.883 34.5795 561.416 31.642C565.947 28.7045 571.333 27.2368 577.575 27.2368C582.019 27.2368 586.167 28.1515 590.016 29.9811C593.863 31.8125 596.984 34.4716 599.378 37.9621V2.72159H613.998V92.0985H599.762L599.378 82.5227C597.325 85.928 594.377 88.6307 590.528 90.6307C586.681 92.6288 582.362 93.6307 577.575 93.6307V93.6326ZM580.782 80.9924C584.629 80.9924 587.92 80.0985 590.657 78.3106C593.391 76.5227 595.531 74.0776 597.07 70.9697C598.608 67.8636 599.378 64.3523 599.378 60.4356C599.378 55.9242 598.5 52.1364 596.749 49.072C594.996 46.0076 592.686 43.7083 589.823 42.178C586.958 40.6458 583.901 39.8807 580.653 39.8807C577.061 39.8807 573.876 40.7538 571.098 42.4981C568.318 44.2443 566.138 46.6269 564.556 49.6477C562.974 52.6705 562.183 56.2235 562.183 60.3087C562.183 64.3939 562.994 67.8636 564.62 70.9697C566.244 74.0776 568.447 76.5246 571.225 78.3106C574.003 80.0985 577.188 80.9924 580.78 80.9924H580.782Z"
        fill="white"
      />
    </svg>
  );
}
function LogoBlack(props: React.SVGAttributes<SVGElement>) {
  return (
    <svg
      width="122"
      height="20"
      viewBox="0 0 122 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10.4809 0C5.55601 0 1.6069 4.0678 2.57894 9.35303C3.79332 13.9258 7.03156 14.8098 8.33288 15.0379C6.67443 14.1583 5.62224 12.292 5.53042 10.136C5.3893 6.8125 7.371 4.69697 10.105 4.69697C11.9516 4.69697 13.5622 5.80795 14.4123 7.45492H19.3805C18.311 3.15795 14.7318 0 10.4809 0Z"
        fill="#1B1B22"
      />
      <path
        d="M12.6131 12.5985L13.9005 13.4769L14.3231 13.7686C13.6559 14.6121 11.8812 17.2856 7.25434 16.0943C5.19098 15.5629 2.98235 14.2886 1.60126 11.528C1.11844 10.5636 0.73497 9.42462 0.491115 8.08788C0.491115 8.08788 0.20323 15.9242 6.5717 19.1076C11.3502 21.4947 15.5752 18.6193 17.5949 16.0277L19.2879 17.278L18.4977 11.197L12.6131 12.5985Z"
        fill="#24BA5E"
      />
      <path
        d="M27.61 18.7121C26.3697 18.7121 25.2566 18.4398 24.2716 17.8951C23.2862 17.3504 22.509 16.5799 21.9401 15.5841C21.3707 14.5883 21.0864 13.4178 21.0864 12.0727C21.0864 10.7277 21.3835 9.51061 21.9785 8.52311C22.573 7.53598 23.3713 6.77386 24.374 6.2375C25.3764 5.70114 26.4891 5.43295 27.7125 5.43295C28.9358 5.43295 30.0696 5.70114 31.0633 6.2375C32.057 6.77386 32.847 7.53598 33.4333 8.52311C34.0196 9.51061 34.3125 10.6936 34.3125 12.0727C34.3125 13.3496 34.0278 14.4902 33.4589 15.4947C32.8896 16.4992 32.0996 17.2867 31.0889 17.8568C30.0779 18.4269 28.9185 18.7121 27.6104 18.7121H27.61ZM27.6356 16.2095C28.3658 16.2095 29.0074 16.0269 29.5598 15.6606C30.1118 15.2947 30.5493 14.7966 30.872 14.1667C31.1947 13.5371 31.3563 12.822 31.3563 12.0216C31.3563 11.2212 31.1947 10.464 30.872 9.85114C30.549 9.23826 30.1114 8.76591 29.5598 8.43371C29.0074 8.10189 28.3748 7.93561 27.6612 7.93561C26.9477 7.93561 26.2635 8.11439 25.7119 8.47197C25.1595 8.82955 24.7393 9.31894 24.4505 9.94015C24.1613 10.5617 24.0171 11.2723 24.0171 12.0723C24.0171 12.9405 24.1827 13.6856 24.5141 14.3068C24.8455 14.9284 25.2868 15.4008 25.8392 15.7239C26.3912 16.0473 26.9899 16.2091 27.6356 16.2091V16.2095Z"
        fill="#1B1B22"
      />
      <path
        d="M41.3964 18.7121C40.3428 18.7121 39.438 18.4905 38.6823 18.0481C37.9262 17.6057 37.3444 16.9716 36.9366 16.1458C36.5288 15.3205 36.3251 14.3455 36.3251 13.222V5.73977H39.2046V12.8133C39.2046 13.8689 39.455 14.7072 39.9565 15.3284C40.4573 15.95 41.201 16.2606 42.1864 16.2606C43.1718 16.2606 43.9701 15.9375 44.5308 15.2902C45.0914 14.6436 45.3716 13.7496 45.3716 12.6087V5.73939H48.251V18.4053H45.448L45.3972 16.6689C44.9725 17.3163 44.4035 17.8182 43.6899 18.1758C42.9764 18.5333 42.212 18.7121 41.3964 18.7121Z"
        fill="#1B1B22"
      />
      <path
        d="M50.9523 18.4057V5.73977H53.7553L53.8061 7.80833C54.2305 7.05947 54.804 6.47652 55.5262 6.05909C56.248 5.64205 57.0847 5.43333 58.0362 5.43333V8.47197H57.2463C56.0906 8.47197 55.2332 8.76174 54.6726 9.34015C54.1119 9.91894 53.8318 10.8212 53.8318 12.047V18.4057H50.9519H50.9523Z"
        fill="#1B1B22"
      />
      <path
        d="M64.9172 18.7121C63.9487 18.7121 63.0525 18.5375 62.2287 18.1886C61.4044 17.8398 60.7334 17.342 60.2153 16.6947C59.6968 16.0481 59.4038 15.2902 59.3361 14.422H62.2411C62.3431 15.0182 62.6361 15.4989 63.1203 15.8648C63.6046 16.2311 64.1946 16.4136 64.8916 16.4136C65.52 16.4136 66.0423 16.2777 66.4588 16.0049C66.8748 15.733 67.0831 15.358 67.0831 14.8814C67.0831 14.4727 66.9385 14.1538 66.6497 13.9239C66.3605 13.6939 66.0039 13.5152 65.5795 13.3875C65.1548 13.2598 64.7131 13.1451 64.2545 13.0428C63.4558 12.8557 62.7129 12.6258 62.0246 12.3534C61.3366 12.0814 60.7797 11.6981 60.3554 11.2042C59.9306 10.7106 59.7183 10.0636 59.7183 9.26326C59.7183 8.46288 59.9389 7.77803 60.381 7.20758C60.8227 6.6375 61.4131 6.19886 62.1522 5.89242C62.8914 5.58599 63.7194 5.43295 64.637 5.43295C66.0299 5.43295 67.1892 5.7822 68.1155 6.47993C69.0415 7.17803 69.5554 8.19962 69.6571 9.54432H66.905C66.8368 8.96591 66.5947 8.52273 66.1786 8.21629C65.7621 7.90985 65.2229 7.75682 64.5606 7.75682C63.983 7.75682 63.5029 7.8678 63.1207 8.08864C62.7385 8.31023 62.5472 8.66743 62.5472 9.16098C62.5472 9.50189 62.6873 9.78258 62.9678 10.0038C63.248 10.2254 63.6049 10.4083 64.038 10.5527C64.4714 10.6973 64.8999 10.8208 65.325 10.9227C66.1914 11.1269 66.9645 11.3655 67.6441 11.6379C68.3234 11.9102 68.863 12.2936 69.2621 12.7871C69.6613 13.2811 69.8695 13.9534 69.8864 14.8045C69.8864 15.6216 69.6654 16.3201 69.2237 16.8985C68.7816 17.4773 68.1871 17.9242 67.44 18.239C66.6922 18.5538 65.8514 18.7114 64.9172 18.7114V18.7121Z"
        fill="#1B1B22"
      />
      <path
        d="M78.0661 18.7121C76.7919 18.7121 75.6792 18.4269 74.7276 17.8568C73.7761 17.2867 73.0328 16.5034 72.4978 15.5076C71.9627 14.5117 71.695 13.367 71.695 12.0731C71.695 10.7792 71.9627 9.56212 72.4978 8.57462C73.0328 7.5875 73.7803 6.81705 74.7404 6.26364C75.6999 5.71061 76.8175 5.43371 78.0913 5.43371C79.3651 5.43371 80.4104 5.70189 81.3277 6.23826C82.245 6.77462 82.9672 7.51099 83.4939 8.44697C84.0204 9.38333 84.2839 10.4477 84.2839 11.639C84.2839 11.8777 84.2794 12.0902 84.2711 12.2773C84.2625 12.4648 84.2414 12.6689 84.2075 12.8902H74.6004C74.6512 13.5542 74.821 14.1458 75.1098 14.6648C75.3986 15.1845 75.7936 15.5886 76.2948 15.8777C76.7956 16.1674 77.3691 16.3117 78.0149 16.3117C78.7111 16.3117 79.3019 16.1583 79.7861 15.8523C80.2704 15.5458 80.648 15.1034 80.9203 14.5242H83.953C83.715 15.2902 83.3369 15.9886 82.8188 16.6182C82.3003 17.2481 81.6466 17.7545 80.8566 18.1375C80.0667 18.5205 79.1366 18.7121 78.0661 18.7121ZM74.626 10.8723H81.3281C81.3111 9.93636 80.9798 9.19583 80.3344 8.65076C79.6886 8.10606 78.9156 7.83371 78.0152 7.83371C77.1997 7.83371 76.4646 8.08068 75.811 8.57424C75.1569 9.06818 74.7619 9.83409 74.626 10.8723Z"
        fill="#1B1B22"
      />
      <path
        d="M91.0372 18.4057C89.7461 18.4057 88.7607 18.0909 88.081 17.461C87.4013 16.8314 87.0617 15.8265 87.0617 14.4477V8.16591H85.2524V5.74015H87.0617V2.67576H89.9667V5.74015H93.0502V8.16591H89.9667V14.4735C89.9667 15.0523 90.1027 15.4481 90.3745 15.661C90.646 15.8739 91.0538 15.9803 91.5979 15.9803H93.0506V18.4061H91.0372V18.4057Z"
        fill="#1B1B22"
      />
      <path
        d="M100.9 18.7121C99.6254 18.7121 98.5127 18.4269 97.5612 17.8568C96.6097 17.2867 95.8664 16.5034 95.3313 15.5076C94.7963 14.5117 94.5285 13.367 94.5285 12.0731C94.5285 10.7792 94.7963 9.56212 95.3313 8.57462C95.8664 7.5875 96.6138 6.81705 97.574 6.26364C98.5335 5.71061 99.651 5.43371 100.925 5.43371C102.199 5.43371 103.244 5.70189 104.161 6.23826C105.079 6.77462 105.801 7.51099 106.328 8.44697C106.854 9.38333 107.118 10.4477 107.118 11.639C107.118 11.8777 107.113 12.0902 107.105 12.2773C107.096 12.4648 107.075 12.6689 107.041 12.8902H97.434C97.4848 13.5542 97.6546 14.1458 97.9434 14.6648C98.2322 15.1845 98.6272 15.5886 99.1284 15.8777C99.6292 16.1674 100.203 16.3117 100.848 16.3117C101.545 16.3117 102.135 16.1583 102.62 15.8523C103.104 15.5458 103.482 15.1034 103.754 14.5242H106.787C106.549 15.2902 106.171 15.9886 105.652 16.6182C105.134 17.2481 104.48 17.7545 103.69 18.1375C102.9 18.5205 101.97 18.7121 100.9 18.7121ZM97.4592 10.8723H104.161C104.144 9.93636 103.813 9.19583 103.168 8.65076C102.522 8.10606 101.749 7.83371 100.848 7.83371C100.033 7.83371 99.2978 8.08068 98.6442 8.57424C97.9901 9.06818 97.5951 9.83409 97.4592 10.8723Z"
        fill="#1B1B22"
      />
      <path
        d="M114.762 18.7121C113.505 18.7121 112.431 18.4095 111.539 17.8057C110.647 17.2015 109.959 16.3973 109.475 15.3924C108.99 14.3883 108.748 13.2644 108.748 12.0216C108.748 10.7788 108.99 9.68485 109.475 8.68902C109.959 7.69318 110.651 6.90152 111.552 6.31402C112.452 5.72652 113.522 5.43295 114.762 5.43295C115.645 5.43295 116.47 5.61591 117.234 5.98182C117.999 6.34811 118.619 6.87992 119.095 7.57803V0.530304H122V18.4057H119.171L119.095 16.4905C118.687 17.1716 118.101 17.7121 117.336 18.1121C116.572 18.5117 115.714 18.7121 114.762 18.7121ZM115.4 16.1841C116.164 16.1841 116.818 16.0053 117.362 15.6477C117.905 15.2902 118.33 14.8011 118.636 14.1795C118.942 13.5583 119.095 12.8561 119.095 12.0727C119.095 11.1705 118.92 10.4129 118.572 9.8C118.224 9.18712 117.765 8.72727 117.196 8.42121C116.627 8.11477 116.019 7.96174 115.374 7.96174C114.66 7.96174 114.027 8.13636 113.475 8.48523C112.923 8.83447 112.49 9.31099 112.176 9.91515C111.861 10.5197 111.704 11.2303 111.704 12.0473C111.704 12.8644 111.865 13.5583 112.188 14.1795C112.511 14.8011 112.949 15.2905 113.501 15.6477C114.053 16.0053 114.686 16.1841 115.399 16.1841H115.4Z"
        fill="#1B1B22"
      />
    </svg>
  );
}
function ArrayUpRight(props: React.SVGAttributes<SVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17 7.00009L6 18.0001M11 5.99997H17C17.4714 5.99997 17.7071 5.99997 17.8536 6.14642C18 6.29286 18 6.52856 18 6.99997V13"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function Lesson(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.96874 8.26321C9.86775 8.68104 9.39048 8.9763 8.43593 9.5668C7.51316 10.1376 7.05177 10.4231 6.67995 10.3083C6.52623 10.2609 6.38617 10.1708 6.27321 10.0467C6 9.74657 6 9.16439 6 8.00002C6 6.83566 6 6.25347 6.27321 5.95332C6.38617 5.82923 6.52623 5.73914 6.67995 5.69171C7.05177 5.57698 7.51316 5.8624 8.43593 6.43324C9.39048 7.02374 9.86775 7.319 9.96874 7.73683C10.0104 7.9093 10.0104 8.09075 9.96874 8.26321Z"
        stroke="#767683"
        strokeWidth="1.2"
        strokeLinejoin="round"
      />
      <path
        d="M1.66699 8.00002C1.66699 5.01446 1.66699 3.52168 2.59449 2.59418C3.52198 1.66669 5.01476 1.66669 8.00032 1.66669C10.9859 1.66669 12.4787 1.66669 13.4062 2.59418C14.3337 3.52168 14.3337 5.01446 14.3337 8.00002C14.3337 10.9856 14.3337 12.4784 13.4062 13.4059C12.4787 14.3334 10.9859 14.3334 8.00032 14.3334C5.01476 14.3334 3.52198 14.3334 2.59449 13.4059C1.66699 12.4784 1.66699 10.9856 1.66699 8.00002Z"
        stroke="#767683"
        strokeWidth="1.2"
      />
    </svg>
  );
}

function Clock(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="7.99967" cy="7.99998" r="6.66667" stroke="#767683" strokeWidth="1.2" />
      <path
        d="M8 5.33331V7.99998L9.33333 9.33331"
        stroke="#767683"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
function Students(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="16"
      height="14"
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.66634 3.66667C8.66634 5.13943 7.47243 6.33333 5.99967 6.33333C4.52691 6.33333 3.33301 5.13943 3.33301 3.66667C3.33301 2.19391 4.52691 1 5.99967 1C7.47243 1 8.66634 2.19391 8.66634 3.66667Z"
        stroke="#767683"
        strokeWidth="1.2"
      />
      <path
        d="M10 6.33333C11.4728 6.33333 12.6667 5.13943 12.6667 3.66667C12.6667 2.19391 11.4728 1 10 1"
        stroke="#767683"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.33301 8.33331H4.66634C2.82539 8.33331 1.33301 9.8257 1.33301 11.6666C1.33301 12.403 1.92996 13 2.66634 13H9.33301C10.0694 13 10.6663 12.403 10.6663 11.6666C10.6663 9.8257 9.17396 8.33331 7.33301 8.33331Z"
        stroke="#767683"
        strokeWidth="1.2"
        strokeLinejoin="round"
      />
      <path
        d="M11.333 8.33331C13.174 8.33331 14.6663 9.8257 14.6663 11.6666C14.6663 12.403 14.0694 13 13.333 13H12.333"
        stroke="#767683"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function BookmarkAdd2(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M11 2C7.22876 2 5.34315 2 4.17157 3.12874C3 4.25748 3 6.07416 3 9.70753V17.9808C3 20.2867 3 21.4396 3.77285 21.8523C5.26947 22.6514 8.0768 19.9852 9.41 19.1824C10.1832 18.7168 10.5698 18.484 11 18.484C11.4302 18.484 11.8168 18.7168 12.59 19.1824C13.9232 19.9852 16.7305 22.6514 18.2272 21.8523C19 21.4396 19 20.2867 19 17.9808V13"
        stroke="#1B1B22"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M17 10L17 2M13 6H21" stroke="#1B1B22" strokeWidth="2" strokeLinecap="round" />
    </svg>
  );
}

function CtaArrow(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="155"
      height="239"
      viewBox="0 0 155 239"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        opacity="0.06"
        d="M102.526 144.244L102.756 162.178L102.8 168.087C90.4706 167.303 53.6349 168.033 34.3275 116.458C25.7189 93.4571 23.1359 64.2144 39.9828 33.0602C45.8623 22.1759 54.0457 11.0823 65.0013 0.0186545C65.0013 0.0186545 -10.566 48.6483 1.49454 129.748C10.5596 190.594 65.4488 211.719 103.131 213.842L102.539 238.052L154.498 190.729L102.522 144.246L102.526 144.244Z"
        fill="#1B1B22"
      />
    </svg>
  );
}

function Facebook(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_274_740)">
        <path
          d="M24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 17.9895 4.3882 22.954 10.125 23.8542V15.4688H7.07812V12H10.125V9.35625C10.125 6.34875 11.9166 4.6875 14.6576 4.6875C15.9701 4.6875 17.3438 4.92188 17.3438 4.92188V7.875H15.8306C14.34 7.875 13.875 8.80008 13.875 9.75V12H17.2031L16.6711 15.4688H13.875V23.8542C19.6118 22.954 24 17.9895 24 12Z"
          fill="white"
        />
        <path
          d="M16.6711 15.4688L17.2031 12H13.875V9.75C13.875 8.80102 14.34 7.875 15.8306 7.875H17.3438V4.92188C17.3438 4.92188 15.9705 4.6875 14.6576 4.6875C11.9166 4.6875 10.125 6.34875 10.125 9.35625V12H7.07812V15.4688H10.125V23.8542C11.3674 24.0486 12.6326 24.0486 13.875 23.8542V15.4688H16.6711Z"
          fill="#1B1B22"
        />
      </g>
      <defs>
        <clipPath id="clip0_274_740">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

function TwitterX(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.9455 23L10.396 15.0901L3.44886 23H0.509766L9.09209 13.2311L0.509766 1H8.05571L13.286 8.45502L19.8393 1H22.7784L14.5943 10.3165L23.4914 23H15.9455ZM19.2185 20.77H17.2398L4.71811 3.23H6.6971L11.7121 10.2532L12.5793 11.4719L19.2185 20.77Z"
        fill="white"
      />
    </svg>
  );
}

function Instagram(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_274_745)">
        <path
          d="M12 2.16094C15.2063 2.16094 15.5859 2.175 16.8469 2.23125C18.0188 2.28281 18.6516 2.47969 19.0734 2.64375C19.6313 2.85938 20.0344 3.12188 20.4516 3.53906C20.8734 3.96094 21.1313 4.35938 21.3469 4.91719C21.5109 5.33906 21.7078 5.97656 21.7594 7.14375C21.8156 8.40937 21.8297 8.78906 21.8297 11.9906C21.8297 15.1969 21.8156 15.5766 21.7594 16.8375C21.7078 18.0094 21.5109 18.6422 21.3469 19.0641C21.1313 19.6219 20.8687 20.025 20.4516 20.4422C20.0297 20.8641 19.6313 21.1219 19.0734 21.3375C18.6516 21.5016 18.0141 21.6984 16.8469 21.75C15.5813 21.8062 15.2016 21.8203 12 21.8203C8.79375 21.8203 8.41406 21.8062 7.15313 21.75C5.98125 21.6984 5.34844 21.5016 4.92656 21.3375C4.36875 21.1219 3.96563 20.8594 3.54844 20.4422C3.12656 20.0203 2.86875 19.6219 2.65313 19.0641C2.48906 18.6422 2.29219 18.0047 2.24063 16.8375C2.18438 15.5719 2.17031 15.1922 2.17031 11.9906C2.17031 8.78438 2.18438 8.40469 2.24063 7.14375C2.29219 5.97187 2.48906 5.33906 2.65313 4.91719C2.86875 4.35938 3.13125 3.95625 3.54844 3.53906C3.97031 3.11719 4.36875 2.85938 4.92656 2.64375C5.34844 2.47969 5.98594 2.28281 7.15313 2.23125C8.41406 2.175 8.79375 2.16094 12 2.16094ZM12 0C8.74219 0 8.33438 0.0140625 7.05469 0.0703125C5.77969 0.126563 4.90313 0.332812 4.14375 0.628125C3.35156 0.9375 2.68125 1.34531 2.01563 2.01562C1.34531 2.68125 0.9375 3.35156 0.628125 4.13906C0.332812 4.90313 0.126563 5.775 0.0703125 7.05C0.0140625 8.33437 0 8.74219 0 12C0 15.2578 0.0140625 15.6656 0.0703125 16.9453C0.126563 18.2203 0.332812 19.0969 0.628125 19.8563C0.9375 20.6484 1.34531 21.3188 2.01563 21.9844C2.68125 22.65 3.35156 23.0625 4.13906 23.3672C4.90313 23.6625 5.775 23.8687 7.05 23.925C8.32969 23.9812 8.7375 23.9953 11.9953 23.9953C15.2531 23.9953 15.6609 23.9812 16.9406 23.925C18.2156 23.8687 19.0922 23.6625 19.8516 23.3672C20.6391 23.0625 21.3094 22.65 21.975 21.9844C22.6406 21.3188 23.0531 20.6484 23.3578 19.8609C23.6531 19.0969 23.8594 18.225 23.9156 16.95C23.9719 15.6703 23.9859 15.2625 23.9859 12.0047C23.9859 8.74688 23.9719 8.33906 23.9156 7.05938C23.8594 5.78438 23.6531 4.90781 23.3578 4.14844C23.0625 3.35156 22.6547 2.68125 21.9844 2.01562C21.3188 1.35 20.6484 0.9375 19.8609 0.632812C19.0969 0.3375 18.225 0.13125 16.95 0.075C15.6656 0.0140625 15.2578 0 12 0Z"
          fill="white"
        />
        <path
          d="M12 5.83594C8.59688 5.83594 5.83594 8.59688 5.83594 12C5.83594 15.4031 8.59688 18.1641 12 18.1641C15.4031 18.1641 18.1641 15.4031 18.1641 12C18.1641 8.59688 15.4031 5.83594 12 5.83594ZM12 15.9984C9.79219 15.9984 8.00156 14.2078 8.00156 12C8.00156 9.79219 9.79219 8.00156 12 8.00156C14.2078 8.00156 15.9984 9.79219 15.9984 12C15.9984 14.2078 14.2078 15.9984 12 15.9984Z"
          fill="white"
        />
        <path
          d="M19.8469 5.59214C19.8469 6.38902 19.2 7.03121 18.4078 7.03121C17.6109 7.03121 16.9688 6.38433 16.9688 5.59214C16.9688 4.79526 17.6156 4.15308 18.4078 4.15308C19.2 4.15308 19.8469 4.79995 19.8469 5.59214Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_274_745">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

function LinkedIn(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_274_750)">
        <path
          d="M22.2283 0H1.77167C1.30179 0 0.851161 0.186657 0.518909 0.518909C0.186657 0.851161 0 1.30179 0 1.77167V22.2283C0 22.6982 0.186657 23.1488 0.518909 23.4811C0.851161 23.8133 1.30179 24 1.77167 24H22.2283C22.6982 24 23.1488 23.8133 23.4811 23.4811C23.8133 23.1488 24 22.6982 24 22.2283V1.77167C24 1.30179 23.8133 0.851161 23.4811 0.518909C23.1488 0.186657 22.6982 0 22.2283 0ZM7.15333 20.445H3.545V8.98333H7.15333V20.445ZM5.34667 7.395C4.93736 7.3927 4.53792 7.2692 4.19873 7.04009C3.85955 6.81098 3.59584 6.48653 3.44088 6.10769C3.28591 5.72885 3.24665 5.31259 3.32803 4.91145C3.40941 4.51032 3.6078 4.14228 3.89816 3.85378C4.18851 3.56529 4.55782 3.36927 4.95947 3.29046C5.36112 3.21165 5.77711 3.25359 6.15495 3.41099C6.53279 3.56838 6.85554 3.83417 7.08247 4.17481C7.30939 4.51546 7.43032 4.91569 7.43 5.325C7.43386 5.59903 7.38251 5.87104 7.27901 6.1248C7.17551 6.37857 7.02198 6.6089 6.82757 6.80207C6.63316 6.99523 6.40185 7.14728 6.14742 7.24915C5.893 7.35102 5.62067 7.40062 5.34667 7.395ZM20.4533 20.455H16.8467V14.1933C16.8467 12.3467 16.0617 11.7767 15.0483 11.7767C13.9783 11.7767 12.9283 12.5833 12.9283 14.24V20.455H9.32V8.99167H12.79V10.58H12.8367C13.185 9.875 14.405 8.67 16.2667 8.67C18.28 8.67 20.455 9.865 20.455 13.365L20.4533 20.455Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_274_750">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

function Youtube(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_274_752)">
        <path
          d="M23.5216 6.18541C23.3859 5.67482 23.1185 5.20883 22.7462 4.83407C22.3738 4.4593 21.9095 4.18891 21.3998 4.04996C19.5234 3.54541 12.0234 3.54541 12.0234 3.54541C12.0234 3.54541 4.52344 3.54541 2.64707 4.04996C2.13737 4.18891 1.6731 4.4593 1.30073 4.83407C0.928354 5.20883 0.660943 5.67482 0.525256 6.18541C0.0234376 8.06996 0.0234375 12 0.0234375 12C0.0234375 12 0.0234376 15.93 0.525256 17.8145C0.660943 18.3251 0.928354 18.7911 1.30073 19.1658C1.6731 19.5406 2.13737 19.811 2.64707 19.95C4.52344 20.4545 12.0234 20.4545 12.0234 20.4545C12.0234 20.4545 19.5234 20.4545 21.3998 19.95C21.9095 19.811 22.3738 19.5406 22.7462 19.1658C23.1185 18.7911 23.3859 18.3251 23.5216 17.8145C24.0234 15.93 24.0234 12 24.0234 12C24.0234 12 24.0234 8.06996 23.5216 6.18541Z"
          fill="white"
        />
        <path d="M9.56934 15.5687V8.4314L15.8421 12L9.56934 15.5687Z" fill="#1B1B22" />
      </g>
      <defs>
        <clipPath id="clip0_274_752">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

function ArrowRightGreen(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10.1667 1.83343L1 11.0001M5.16667 1H10.1667C10.5595 1 10.7559 1 10.878 1.12204C11 1.24408 11 1.4405 11 1.83333V6.83333"
        stroke="#0F9D46"
        strokeWidth="1.75"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function RatingStar(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.00001 12.8L3.20001 15.2L4.00001 10.4L0.800018 6.40002L5.60001 6.40002L8.00001 1.60002L10.4 6.40002H15.2L11 10.4L11.8 15.2L8.00001 12.8Z"
        fill="#FFB800"
      />
    </svg>
  );
}

function VideoCameraIconLanding(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="56"
      height="56"
      viewBox="0 0 56 56"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_331_424)">
        <rect width="56" height="56" rx="28" fill="#24BA5E" />
        <g filter="url(#filter0_f_331_424)">
          <circle cx="44.5" cy="11.5" r="11.5" fill="white" />
        </g>
        <g filter="url(#filter1_f_331_424)">
          <circle cx="32" cy="65" r="25" fill="#8DFF46" />
        </g>
        <path
          d="M31.8281 24.9583L39.3137 20.6511C40.3047 20.0703 41.125 20.4805 41.125 21.677V34.3919C41.125 35.5545 40.3047 36.0325 39.3137 35.4517L31.8281 31.1106V34.3241C31.8281 35.5206 30.8711 36.4777 29.6745 36.4777H16.9597C15.797 36.4777 14.875 35.5206 14.875 34.3241V21.6431C14.875 20.4466 15.7981 19.5234 16.9597 19.5234H29.6745C30.8711 19.5234 31.8281 20.4466 31.8281 21.6431V24.9583Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_f_331_424"
          x="9"
          y="-24"
          width="71"
          height="71"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="12" result="effect1_foregroundBlur_331_424" />
        </filter>
        <filter
          id="filter1_f_331_424"
          x="-17"
          y="16"
          width="98"
          height="98"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="12" result="effect1_foregroundBlur_331_424" />
        </filter>
        <clipPath id="clip0_331_424">
          <rect width="56" height="56" rx="28" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

function SprayedDesignLanding(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="160"
      height="64"
      viewBox="0 0 160 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g opacity="0.2">
        <circle cx="2" cy="2" r="2" fill="#53C57F" />
        <circle cx="14" cy="2" r="2" fill="#53C57F" />
        <circle cx="26" cy="2" r="2" fill="#53C57F" />
        <circle cx="38" cy="2" r="2" fill="#53C57F" />
        <circle cx="50" cy="2" r="2" fill="#53C57F" />
        <circle cx="62" cy="2" r="2" fill="#53C57F" />
        <circle cx="74" cy="2" r="2" fill="#53C57F" />
        <circle cx="86" cy="2" r="2" fill="#53C57F" />
        <circle cx="98" cy="2" r="2" fill="#53C57F" />
        <circle cx="110" cy="2" r="2" fill="#53C57F" />
        <circle cx="122" cy="2" r="2" fill="#53C57F" />
        <circle cx="134" cy="2" r="2" fill="#53C57F" />
        <circle cx="146" cy="2" r="2" fill="#53C57F" />
        <circle cx="158" cy="2" r="2" fill="#53C57F" />
        <circle cx="2" cy="14" r="2" fill="#53C57F" />
        <circle cx="14" cy="14" r="2" fill="#53C57F" />
        <circle cx="26" cy="14" r="2" fill="#53C57F" />
        <circle cx="38" cy="14" r="2" fill="#53C57F" />
        <circle cx="50" cy="14" r="2" fill="#53C57F" />
        <circle cx="62" cy="14" r="2" fill="#53C57F" />
        <circle cx="74" cy="14" r="2" fill="#53C57F" />
        <circle cx="86" cy="14" r="2" fill="#53C57F" />
        <circle cx="98" cy="14" r="2" fill="#53C57F" />
        <circle cx="110" cy="14" r="2" fill="#53C57F" />
        <circle cx="122" cy="14" r="2" fill="#53C57F" />
        <circle cx="134" cy="14" r="2" fill="#53C57F" />
        <circle cx="146" cy="14" r="2" fill="#53C57F" />
        <circle cx="158" cy="14" r="2" fill="#53C57F" />
        <circle cx="2" cy="26" r="2" fill="#53C57F" />
        <circle cx="14" cy="26" r="2" fill="#53C57F" />
        <circle cx="26" cy="26" r="2" fill="#53C57F" />
        <circle cx="38" cy="26" r="2" fill="#53C57F" />
        <circle cx="50" cy="26" r="2" fill="#53C57F" />
        <circle cx="62" cy="26" r="2" fill="#53C57F" />
        <circle cx="74" cy="26" r="2" fill="#53C57F" />
        <circle cx="86" cy="26" r="2" fill="#53C57F" />
        <circle cx="98" cy="26" r="2" fill="#53C57F" />
        <circle cx="110" cy="26" r="2" fill="#53C57F" />
        <circle cx="122" cy="26" r="2" fill="#53C57F" />
        <circle cx="134" cy="26" r="2" fill="#53C57F" />
        <circle cx="146" cy="26" r="2" fill="#53C57F" />
        <circle cx="158" cy="26" r="2" fill="#53C57F" />
        <circle cx="2" cy="38" r="2" fill="#53C57F" />
        <circle cx="14" cy="38" r="2" fill="#53C57F" />
        <circle cx="26" cy="38" r="2" fill="#53C57F" />
        <circle cx="38" cy="38" r="2" fill="#53C57F" />
        <circle cx="50" cy="38" r="2" fill="#53C57F" />
        <circle cx="62" cy="38" r="2" fill="#53C57F" />
        <circle cx="74" cy="38" r="2" fill="#53C57F" />
        <circle cx="86" cy="38" r="2" fill="#53C57F" />
        <circle cx="98" cy="38" r="2" fill="#53C57F" />
        <circle cx="110" cy="38" r="2" fill="#53C57F" />
        <circle cx="122" cy="38" r="2" fill="#53C57F" />
        <circle cx="134" cy="38" r="2" fill="#53C57F" />
        <circle cx="146" cy="38" r="2" fill="#53C57F" />
        <circle cx="158" cy="38" r="2" fill="#53C57F" />
        <circle cx="2" cy="50" r="2" fill="#53C57F" />
        <circle cx="14" cy="50" r="2" fill="#53C57F" />
        <circle cx="26" cy="50" r="2" fill="#53C57F" />
        <circle cx="38" cy="50" r="2" fill="#53C57F" />
        <circle cx="50" cy="50" r="2" fill="#53C57F" />
        <circle cx="62" cy="50" r="2" fill="#53C57F" />
        <circle cx="74" cy="50" r="2" fill="#53C57F" />
        <circle cx="86" cy="50" r="2" fill="#53C57F" />
        <circle cx="98" cy="50" r="2" fill="#53C57F" />
        <circle cx="110" cy="50" r="2" fill="#53C57F" />
        <circle cx="122" cy="50" r="2" fill="#53C57F" />
        <circle cx="134" cy="50" r="2" fill="#53C57F" />
        <circle cx="146" cy="50" r="2" fill="#53C57F" />
        <circle cx="158" cy="50" r="2" fill="#53C57F" />
        <circle cx="2" cy="62" r="2" fill="#53C57F" />
        <circle cx="14" cy="62" r="2" fill="#53C57F" />
        <circle cx="26" cy="62" r="2" fill="#53C57F" />
        <circle cx="38" cy="62" r="2" fill="#53C57F" />
        <circle cx="50" cy="62" r="2" fill="#53C57F" />
        <circle cx="62" cy="62" r="2" fill="#53C57F" />
        <circle cx="74" cy="62" r="2" fill="#53C57F" />
        <circle cx="86" cy="62" r="2" fill="#53C57F" />
        <circle cx="98" cy="62" r="2" fill="#53C57F" />
        <circle cx="110" cy="62" r="2" fill="#53C57F" />
        <circle cx="122" cy="62" r="2" fill="#53C57F" />
        <circle cx="134" cy="62" r="2" fill="#53C57F" />
        <circle cx="146" cy="62" r="2" fill="#53C57F" />
        <circle cx="158" cy="62" r="2" fill="#53C57F" />
        <rect width="160" height="64" fill="url(#paint0_linear_331_525)" />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_331_525"
          x1="160"
          y1="0"
          x2="38"
          y2="64"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="white" stopOpacity="0" />
          <stop offset="1" stopColor="white" />
        </linearGradient>
      </defs>
    </svg>
  );
}

function HeroStarIcon(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M18 0L23.0912 12.9088L36 18L23.0912 23.0912L18 36L12.9088 23.0912L0 18L12.9088 12.9088L18 0Z"
        fill="#EDAE38"
      />
    </svg>
  );
}

function HeroStarIconSmall(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10 0L12.8284 7.17157L20 10L12.8284 12.8284L10 20L7.17157 12.8284L0 10L7.17157 7.17157L10 0Z"
        fill="#EDAE38"
      />
    </svg>
  );
}

function HeroStarIconBig(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M24 0L30.7882 17.2118L48 24L30.7882 30.7882L24 48L17.2118 30.7882L0 24L17.2118 17.2118L24 0Z"
        fill="#EDAE38"
      />
    </svg>
  );
}

function ArrowRight(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="8"
      height="12"
      viewBox="0 0 8 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M1.5 11L5.91074 6.58926C6.18852 6.31149 6.32741 6.1726 6.32741 6.00001C6.32741 5.82742 6.18852 5.68853 5.91074 5.41075L1.5 1.00001"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function ArrowLeft(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="8"
      height="12"
      viewBox="0 0 8 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.5 1L2.08926 5.41074C1.81148 5.68852 1.67259 5.82741 1.67259 6C1.67259 6.17259 1.81148 6.31148 2.08926 6.58926L6.5 11"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function CrossIcon(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="10"
      height="10"
      viewBox="0 0 10 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.50024 1.49994L5.00024 4.99994M5.00024 4.99994L1.50024 8.49994M5.00024 4.99994L1.50024 1.49994M5.00024 4.99994L8.50024 8.49994"
        stroke="#767683"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
function ArrowUpRight01(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M22.6667 9.33345L8 24.0001M14.6672 7.99995H22.6672C23.2957 7.99995 23.61 7.99995 23.8053 8.19522C24.0005 8.39048 24.0005 8.70475 24.0005 9.33329V17.3333"
        stroke="currentColor"
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
function Tick_02(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 10 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.5 1.25L3.25 6.75L1.5 5"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function Remove_01(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 10 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9 1H1"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
function InputCircleTick(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      style={{
        display: 'block',
        alignSelf: 'center',
        ...props.style,
      }}
    >
      <path
        d="M13.9783 8.09125C14.3048 7.73502 14.2808 7.18152 13.9246 6.85498C13.5683 6.52844 13.0148 6.5525 12.6883 6.90873L13.9783 8.09125ZM8.74996 12.5L8.13124 13.1187C8.3 13.2875 8.53037 13.38 8.76897 13.3748C9.00757 13.3696 9.2337 13.2672 9.39497 13.0912L8.74996 12.5ZM7.28534 9.79794C6.94364 9.45623 6.38962 9.45623 6.04791 9.79794C5.7062 10.1396 5.7062 10.6937 6.04791 11.0354L7.28534 9.79794ZM18.3333 9.99999H19.2083C19.2083 4.91437 15.0856 0.791656 9.99996 0.791656V1.66666V2.54166C14.1191 2.54166 17.4583 5.88087 17.4583 9.99999H18.3333ZM9.99996 1.66666V0.791656C4.91434 0.791656 0.791626 4.91437 0.791626 9.99999H1.66663H2.54163C2.54163 5.88087 5.88084 2.54166 9.99996 2.54166V1.66666ZM1.66663 9.99999H0.791626C0.791626 15.0856 4.91434 19.2083 9.99996 19.2083V18.3333V17.4583C5.88084 17.4583 2.54163 14.1191 2.54163 9.99999H1.66663ZM9.99996 18.3333V19.2083C15.0856 19.2083 19.2083 15.0856 19.2083 9.99999H18.3333H17.4583C17.4583 14.1191 14.1191 17.4583 9.99996 17.4583V18.3333ZM13.3333 7.49999L12.6883 6.90873L8.10495 11.9087L8.74996 12.5L9.39497 13.0912L13.9783 8.09125L13.3333 7.49999ZM8.74996 12.5L9.36868 11.8813L7.28534 9.79794L6.66663 10.4167L6.04791 11.0354L8.13124 13.1187L8.74996 12.5Z"
        fill="#767683"
      />
    </svg>
  );
}

function ArrowDown(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="12"
      height="8"
      viewBox="0 0 12 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M1 1.5L5.41074 5.91075C5.68852 6.18852 5.82741 6.32741 6 6.32741C6.17259 6.32741 6.31148 6.18852 6.58926 5.91075L11 1.5"
        stroke="#767683"
        strokeWidth="1.75"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
function ViewOn(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="20"
      height="14"
      viewBox="0 0 20 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17.9533 6.20414C18.2066 6.55941 18.3333 6.73704 18.3333 6.99999C18.3333 7.26294 18.2066 7.44057 17.9533 7.79584C16.8149 9.39212 13.9076 12.8333 9.99996 12.8333C6.09228 12.8333 3.18504 9.39212 2.04666 7.79584C1.7933 7.44057 1.66663 7.26294 1.66663 6.99999C1.66663 6.73704 1.7933 6.55941 2.04666 6.20414C3.18504 4.60786 6.09227 1.16666 9.99996 1.16666C13.9076 1.16666 16.8149 4.60786 17.9533 6.20414Z"
        stroke="#767683"
        strokeWidth="1.75"
      />
      <path
        d="M12.5 6.99999C12.5 5.61928 11.3807 4.49999 9.99996 4.49999C8.61925 4.49999 7.49996 5.61928 7.49996 6.99999C7.49996 8.3807 8.61925 9.49999 9.99996 9.49999C11.3807 9.49999 12.5 8.3807 12.5 6.99999Z"
        stroke="#767683"
        strokeWidth="1.75"
      />
    </svg>
  );
}

function ViewOff(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="22"
      height="10"
      viewBox="0 0 22 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M21.8321 1.5547C22.1384 1.09517 22.0142 0.474302 21.5547 0.16795C21.0952 -0.138403 20.4743 -0.0142289 20.1679 0.4453L21.8321 1.5547ZM1.83205 0.4453C1.5257 -0.0142289 0.904828 -0.138403 0.4453 0.16795C-0.0142289 0.474302 -0.138403 1.09517 0.16795 1.5547L1.83205 0.4453ZM14.8575 5.9855C14.5733 5.51192 13.9591 5.35836 13.4855 5.64251C13.0119 5.92666 12.8584 6.54092 13.1425 7.0145L14.8575 5.9855ZM14.6425 9.5145C14.9267 9.98808 15.5409 10.1416 16.0145 9.85749C16.4881 9.57335 16.6416 8.95908 16.3575 8.4855L14.6425 9.5145ZM19.7071 3.29289C19.3166 2.90237 18.6834 2.90237 18.2929 3.29289C17.9024 3.68342 17.9024 4.31658 18.2929 4.70711L19.7071 3.29289ZM20.2929 6.70711C20.6834 7.09763 21.3166 7.09763 21.7071 6.70711C22.0976 6.31658 22.0976 5.68342 21.7071 5.29289L20.2929 6.70711ZM0.292893 5.29289C-0.0976311 5.68342 -0.0976311 6.31658 0.292893 6.70711C0.683417 7.09763 1.31658 7.09763 1.70711 6.70711L0.292893 5.29289ZM3.70711 4.70711C4.09763 4.31658 4.09763 3.68342 3.70711 3.29289C3.31658 2.90237 2.68342 2.90237 2.29289 3.29289L3.70711 4.70711ZM8.85749 7.0145C9.14164 6.54092 8.98808 5.92666 8.5145 5.64251C8.04092 5.35836 7.42666 5.51192 7.14251 5.9855L8.85749 7.0145ZM5.64251 8.4855C5.35836 8.95908 5.51192 9.57335 5.9855 9.85749C6.45908 10.1416 7.07334 9.98808 7.35749 9.5145L5.64251 8.4855ZM21 1C20.1679 0.4453 20.1681 0.445041 20.1683 0.444805C20.1683 0.444757 20.1685 0.444543 20.1685 0.444446C20.1686 0.444251 20.1687 0.444147 20.1687 0.444131C20.1688 0.4441 20.1685 0.444426 20.1681 0.445102C20.1672 0.446455 20.1653 0.449211 20.1625 0.453324C20.1569 0.461552 20.1475 0.475205 20.1344 0.49392C20.1082 0.531362 20.0672 0.589003 20.0118 0.66396C19.9011 0.813975 19.7335 1.03273 19.513 1.29732C19.0709 1.82781 18.4223 2.53528 17.5992 3.24074C15.9373 4.66519 13.6621 6 11 6V8C14.3379 8 17.0627 6.33481 18.9008 4.75926C19.8277 3.96472 20.5541 3.17219 21.0495 2.57768C21.2977 2.27977 21.4895 2.02977 21.621 1.85166C21.6867 1.76256 21.7375 1.69129 21.7728 1.64085C21.7905 1.61562 21.8043 1.59558 21.8142 1.5811C21.8191 1.57386 21.8231 1.56801 21.8261 1.5636C21.8276 1.56139 21.8288 1.55954 21.8298 1.55805C21.8303 1.55731 21.8307 1.55666 21.8311 1.5561C21.8313 1.55582 21.8315 1.55547 21.8316 1.55533C21.8318 1.555 21.8321 1.5547 21 1ZM11 6C8.33791 6 6.06265 4.66519 4.40079 3.24074C3.57775 2.53528 2.92905 1.82781 2.48697 1.29732C2.26649 1.03273 2.09889 0.813975 1.98817 0.66396C1.93284 0.589003 1.89183 0.531362 1.86562 0.49392C1.85252 0.475205 1.84313 0.461552 1.83751 0.453324C1.8347 0.449211 1.83284 0.446455 1.83192 0.445102C1.83147 0.444426 1.83125 0.4441 1.83127 0.444131C1.83128 0.444147 1.83135 0.444251 1.83148 0.444446C1.83155 0.444543 1.83169 0.444757 1.83172 0.444805C1.83188 0.445041 1.83205 0.4453 1 1C0.16795 1.5547 0.168153 1.555 0.168371 1.55533C0.168465 1.55547 0.168699 1.55582 0.168886 1.5561C0.16926 1.55666 0.169696 1.55731 0.170193 1.55805C0.171189 1.55954 0.172432 1.56139 0.173921 1.5636C0.176899 1.56801 0.180863 1.57386 0.185806 1.5811C0.195691 1.59558 0.209494 1.61562 0.227155 1.64085C0.262469 1.69129 0.313252 1.76256 0.37902 1.85166C0.510482 2.02977 0.702264 2.27977 0.950529 2.57768C1.44595 3.17219 2.17225 3.96472 3.09921 4.75926C4.93735 6.33481 7.66209 8 11 8V6ZM13.1425 7.0145L14.6425 9.5145L16.3575 8.4855L14.8575 5.9855L13.1425 7.0145ZM18.2929 4.70711L20.2929 6.70711L21.7071 5.29289L19.7071 3.29289L18.2929 4.70711ZM1.70711 6.70711L3.70711 4.70711L2.29289 3.29289L0.292893 5.29289L1.70711 6.70711ZM7.14251 5.9855L5.64251 8.4855L7.35749 9.5145L8.85749 7.0145L7.14251 5.9855Z"
        fill="#1B1B22"
      />
    </svg>
  );
}

function Google(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{
        display: 'block',
        alignSelf: 'center',
        ...props.style,
      }}
      {...props}
    >
      <g clipPath="url(#clip0_331_717)">
        <path
          d="M23.766 12.2764C23.766 11.4607 23.6999 10.6406 23.5588 9.83807H12.24V14.4591H18.7217C18.4528 15.9494 17.5885 17.2678 16.323 18.1056V21.1039H20.19C22.4608 19.0139 23.766 15.9274 23.766 12.2764Z"
          fill="#4285F4"
        />
        <path
          d="M12.24 24.0008C15.4764 24.0008 18.2058 22.9382 20.1944 21.1039L16.3274 18.1055C15.2516 18.8375 13.8626 19.252 12.2444 19.252C9.11376 19.252 6.45934 17.1399 5.50693 14.3003H1.51648V17.3912C3.55359 21.4434 7.70278 24.0008 12.24 24.0008Z"
          fill="#34A853"
        />
        <path
          d="M5.50253 14.3003C4.99987 12.8099 4.99987 11.1961 5.50253 9.70575V6.61481H1.51649C-0.18551 10.0056 -0.18551 14.0004 1.51649 17.3912L5.50253 14.3003Z"
          fill="#FBBC04"
        />
        <path
          d="M12.24 4.74966C13.9508 4.7232 15.6043 5.36697 16.8433 6.54867L20.2694 3.12262C18.1 1.0855 15.2207 -0.034466 12.24 0.000808666C7.70277 0.000808666 3.55359 2.55822 1.51648 6.61481L5.50252 9.70575C6.45052 6.86173 9.10935 4.74966 12.24 4.74966Z"
          fill="#EA4335"
        />
      </g>
      <defs>
        <clipPath id="clip0_331_717">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
function LinkedInColored(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{
        display: 'block',
        alignSelf: 'center',
        ...props.style,
      }}
      {...props}
    >
      <g clipPath="url(#clip0_331_715)">
        <path
          d="M22.2283 -1.90735e-06H1.77167C1.30179 -1.90735e-06 0.851161 0.186656 0.518909 0.518908C0.186657 0.85116 0 1.30179 0 1.77166V22.2283C0 22.6982 0.186657 23.1488 0.518909 23.4811C0.851161 23.8133 1.30179 24 1.77167 24H22.2283C22.6982 24 23.1488 23.8133 23.4811 23.4811C23.8133 23.1488 24 22.6982 24 22.2283V1.77166C24 1.30179 23.8133 0.85116 23.4811 0.518908C23.1488 0.186656 22.6982 -1.90735e-06 22.2283 -1.90735e-06ZM7.15333 20.445H3.545V8.98333H7.15333V20.445ZM5.34667 7.395C4.93736 7.39269 4.53792 7.26919 4.19873 7.04009C3.85955 6.81098 3.59584 6.48653 3.44088 6.10769C3.28591 5.72885 3.24665 5.31259 3.32803 4.91145C3.40941 4.51032 3.6078 4.14227 3.89816 3.85378C4.18851 3.56528 4.55782 3.36927 4.95947 3.29046C5.36112 3.21165 5.77711 3.25359 6.15495 3.41098C6.53279 3.56838 6.85554 3.83417 7.08247 4.17481C7.30939 4.51546 7.43032 4.91569 7.43 5.325C7.43386 5.59903 7.38251 5.87104 7.27901 6.1248C7.17551 6.37857 7.02198 6.6089 6.82757 6.80206C6.63316 6.99523 6.40185 7.14728 6.14742 7.24915C5.893 7.35102 5.62067 7.40062 5.34667 7.395ZM20.4533 20.455H16.8467V14.1933C16.8467 12.3467 16.0617 11.7767 15.0483 11.7767C13.9783 11.7767 12.9283 12.5833 12.9283 14.24V20.455H9.32V8.99167H12.79V10.58H12.8367C13.185 9.875 14.405 8.67 16.2667 8.67C18.28 8.67 20.455 9.865 20.455 13.365L20.4533 20.455Z"
          fill="#0A66C2"
        />
      </g>
      <defs>
        <clipPath id="clip0_331_715">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
function SquareLock(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      stroke="currentColor"
      style={{ display: 'block', margin: 'auto' }}
      {...props}
    >
      <path
        d="M19.7322 12.1553L20.7232 12.0218L19.7322 12.1553ZM16.4403 9.09909L16.4863 8.10015L16.4403 9.09909ZM16.4403 21.9009L16.4863 22.8999L16.4403 21.9009ZM19.7322 18.8447L18.7411 18.7113L19.7322 18.8447ZM7.55966 9.09909L7.60558 10.098L7.55966 9.09909ZM4.26781 12.1553L3.27675 12.0218L4.26781 12.1553ZM7.55966 21.9009L7.60558 20.902L7.55966 21.9009ZM4.26781 18.8447L5.25887 18.7113L4.26781 18.8447ZM6.5 9C6.5 9.55228 6.94772 10 7.5 10C8.05229 10 8.5 9.55228 8.5 9H6.5ZM15.5 9C15.5 9.55228 15.9477 10 16.5 10C17.0523 10 17.5 9.55228 17.5 9H15.5ZM11.9961 14.5C11.4438 14.5 10.9961 14.9477 10.9961 15.5C10.9961 16.0523 11.4438 16.5 11.9961 16.5V14.5ZM12.0051 16.5C12.5574 16.5 13.0051 16.0523 13.0051 15.5C13.0051 14.9477 12.5574 14.5 12.0051 14.5V16.5ZM12 21C10.4302 21 9.00645 20.9664 7.60558 20.902L7.51374 22.8999C8.9461 22.9657 10.4004 23 12 23V21ZM16.3944 20.902C14.9936 20.9664 13.5698 21 12 21V23C13.5996 23 15.0539 22.9657 16.4863 22.8999L16.3944 20.902ZM20.7232 18.9782C20.8719 17.8744 21 16.705 21 15.5H19C19 16.5702 18.886 17.635 18.7411 18.7113L20.7232 18.9782ZM21 15.5C21 14.295 20.8719 13.1256 20.7232 12.0218L18.7411 12.2887C18.886 13.365 19 14.4298 19 15.5H21ZM12 10C13.5698 10 14.9936 10.0336 16.3944 10.098L16.4863 8.10015C15.0539 8.0343 13.5996 8 12 8V10ZM7.60558 10.098C9.00645 10.0336 10.4302 10 12 10V8C10.4005 8 8.9461 8.0343 7.51373 8.10015L7.60558 10.098ZM3.27675 12.0218C3.12813 13.1256 3 14.295 3 15.5H5C5 14.4298 5.11396 13.365 5.25887 12.2887L3.27675 12.0218ZM3 15.5C3 16.705 3.12814 17.8744 3.27675 18.9782L5.25887 18.7113C5.11396 17.635 5 16.5702 5 15.5H3ZM20.7232 12.0218C20.4361 9.88922 18.6692 8.2005 16.4863 8.10015L16.3944 10.098C17.5786 10.1525 18.5785 11.0808 18.7411 12.2887L20.7232 12.0218ZM16.4863 22.8999C18.6692 22.7995 20.4361 21.1108 20.7232 18.9782L18.7411 18.7113C18.5785 19.9192 17.5786 20.8475 16.3944 20.902L16.4863 22.8999ZM7.51373 8.10015C5.33084 8.2005 3.56389 9.88922 3.27675 12.0218L5.25887 12.2887C5.42149 11.0808 6.42142 10.1525 7.60558 10.098L7.51373 8.10015ZM7.60558 20.902C6.42142 20.8475 5.4215 19.9192 5.25887 18.7113L3.27675 18.9782C3.56389 21.1108 5.33084 22.7995 7.51374 22.8999L7.60558 20.902ZM8.5 9V6.5H6.5V9H8.5ZM15.5 6.5V9H17.5V6.5H15.5ZM12 3C13.933 3 15.5 4.567 15.5 6.5H17.5C17.5 3.46243 15.0376 1 12 1V3ZM8.5 6.5C8.5 4.567 10.067 3 12 3V1C8.96244 1 6.5 3.46243 6.5 6.5H8.5ZM11.9961 16.5H12.0051V14.5H11.9961V16.5Z"
        fill="#1B1B22"
      />
    </svg>
  );
}

function Mail2(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      style={{ display: 'block', margin: 'auto' }}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M7 8.49999L9.94202 10.2394C11.6572 11.2535 12.3428 11.2535 14.058 10.2394L17 8.49999M9.09882 3.53653C11.0393 3.48778 12.9607 3.48778 14.9012 3.53654C18.0497 3.61565 19.6239 3.6552 20.7551 4.79063C21.8862 5.92606 21.9189 7.45883 21.9842 10.5244C22.0053 11.51 22.0053 12.4899 21.9842 13.4756C21.9189 16.5411 21.8862 18.0739 20.7551 19.2093C19.6239 20.3448 18.0497 20.3843 14.9012 20.4634C12.9607 20.5122 11.0393 20.5122 9.09883 20.4634C5.95033 20.3843 4.37608 20.3448 3.24496 19.2093C2.11383 18.0739 2.08114 16.5411 2.01577 13.4756C1.99475 12.4899 1.99474 11.51 2.01576 10.5243C2.08114 7.45882 2.11382 5.92605 3.24495 4.79062C4.37608 3.65518 5.95033 3.61563 9.09882 3.53653Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function Search(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17.5 17.5L22 22M20 11C20 6.02944 15.9706 2 11 2C6.02944 2 2 6.02944 2 11C2 15.9706 6.02944 20 11 20C15.9706 20 20 15.9706 20 11Z"
        stroke="#1B1B22"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function ShoppingCart(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M7.91695 15.0035C7.36658 15.0493 6.95759 15.5327 7.00345 16.083C7.04932 16.6334 7.53267 17.0424 8.08305 16.9965L7.91695 15.0035ZM16.7201 15.2733L16.8032 16.2699L16.7201 15.2733ZM20.3635 11.7289L21.3573 11.8393L20.3635 11.7289ZM21.9939 6.11043C22.0549 5.56152 21.6593 5.06711 21.1104 5.00612C20.5615 4.94513 20.0671 5.34066 20.0061 5.88957L21.9939 6.11043ZM6 5C5.44772 5 5 5.44772 5 6C5 6.55228 5.44772 7 6 7V5ZM22 7C22.5523 7 23 6.55228 23 6C23 5.44772 22.5523 5 22 5V7ZM7.93852 15.0765L6.97007 15.3257V15.3257L7.93852 15.0765ZM4.96326 3.51493L3.99481 3.76415L4.96326 3.51493ZM2 1C1.44772 1 1 1.44772 1 2C1 2.55228 1.44772 3 2 3V1ZM7.58824 16.7616L8.37979 17.3727H8.37979L7.58824 16.7616ZM5.84058 17.3889C5.50308 17.8261 5.58387 18.4541 6.02103 18.7916C6.4582 19.1291 7.08619 19.0483 7.42369 18.6111L5.84058 17.3889ZM8.08305 16.9965L16.8032 16.2699L16.6371 14.2768L7.91695 15.0035L8.08305 16.9965ZM21.3573 11.8393L21.9939 6.11043L20.0061 5.88957L19.3696 11.6184L21.3573 11.8393ZM16.8032 16.2699C18.1572 16.157 19.335 15.9448 20.1489 15.153C20.9628 14.3613 21.2073 13.1897 21.3573 11.8393L19.3696 11.6184C19.2173 12.9892 19.0044 13.4762 18.7543 13.7195C18.5042 13.9628 18.0115 14.1622 16.6371 14.2768L16.8032 16.2699ZM6 7H22V5H6V7ZM7 20C7 20.5523 6.55228 21 6 21V23C7.65685 23 9 21.6569 9 20H7ZM6 21C5.44772 21 5 20.5523 5 20H3C3 21.6569 4.34315 23 6 23V21ZM5 20C5 19.4477 5.44772 19 6 19V17C4.34315 17 3 18.3431 3 20H5ZM6 19C6.55228 19 7 19.4477 7 20H9C9 18.3431 7.65685 17 6 17V19ZM18 20C18 20.5523 17.5523 21 17 21V23C18.6569 23 20 21.6569 20 20H18ZM17 21C16.4477 21 16 20.5523 16 20H14C14 21.6569 15.3431 23 17 23V21ZM16 20C16 19.4477 16.4477 19 17 19V17C15.3431 17 14 18.3431 14 20H16ZM17 19C17.5523 19 18 19.4477 18 20H20C20 18.3431 18.6569 17 17 17V19ZM8 21H15V19H8V21ZM8.90696 14.8273L5.93171 3.26571L3.99481 3.76415L6.97007 15.3257L8.90696 14.8273ZM2.966 1H2V3H2.966V1ZM5.93171 3.26571C5.58447 1.91637 4.34908 1 2.966 1V3C3.47229 3 3.88381 3.33282 3.99481 3.76415L5.93171 3.26571ZM6.79668 16.1505L5.84058 17.3889L7.42369 18.6111L8.37979 17.3727L6.79668 16.1505ZM6.97007 15.3257C7.04276 15.6082 6.98213 15.9102 6.79668 16.1505L8.37979 17.3727C8.93828 16.6492 9.13499 15.7134 8.90696 14.8273L6.97007 15.3257Z"
        fill="#1B1B22"
      />
    </svg>
  );
}

function ArrowRight2(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        stroke="currentColor"
        {...props}
      >
        <path
          d="M19.0001 11.9998H4.00013M15 6.99991L19.2928 11.2928C19.6262 11.6261 19.7928 11.7928 19.7928 11.9999C19.7928 12.207 19.6262 12.3737 19.2928 12.707L15 16.9999"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
}

function Menu(props: React.SVGAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M13.5 4.5C13.5 3.67157 12.8284 3 12 3C11.1716 3 10.5 3.67157 10.5 4.5C10.5 5.32843 11.1716 6 12 6C12.8284 6 13.5 5.32843 13.5 4.5Z"
        stroke="#1B1B22"
        strokeWidth="2"
      />
      <path
        d="M13.5 12C13.5 11.1716 12.8284 10.5 12 10.5C11.1716 10.5 10.5 11.1716 10.5 12C10.5 12.8284 11.1716 13.5 12 13.5C12.8284 13.5 13.5 12.8284 13.5 12Z"
        stroke="#1B1B22"
        strokeWidth="2"
      />
      <path
        d="M13.5 19.5C13.5 18.6716 12.8284 18 12 18C11.1716 18 10.5 18.6716 10.5 19.5C10.5 20.3284 11.1716 21 12 21C12.8284 21 13.5 20.3284 13.5 19.5Z"
        stroke="#1B1B22"
        strokeWidth="2"
      />
    </svg>
  );
}
