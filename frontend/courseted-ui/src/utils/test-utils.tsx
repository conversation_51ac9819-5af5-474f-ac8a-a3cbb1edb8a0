 

import React from 'react';
import { render as rtlRender } from '@testing-library/react';
import { Provider } from 'react-redux';
import { ThemeProvider } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import { theme } from '@theme';
import authReducer from '@features/auth/authSlice';

// Define proper types for the render function
type RenderOptions = {
  preloadedState?: Record<string, unknown>;
  store?: ReturnType<typeof configureStore>;
  [key: string]: any;
};

function render(
  ui: React.ReactElement,
  {
    // eslint-disable-next-line
    preloadedState = {},
    store = configureStore({
      reducer: authReducer,
      // preloadedState, // Ensure preloadedState is passed to configureStore
    }),
    ...renderOptions
  }: RenderOptions = {}
) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <Provider store={store}>
        <ThemeProvider theme={theme}>{children}</ThemeProvider>
      </Provider>
    );
  }
  return rtlRender(ui, { wrapper: Wrapper, ...renderOptions });
}

// eslint-disable-next-line react-refresh/only-export-components
export * from '@testing-library/react';
export { render };
