import { createTheme, responsiveFontSizes } from '@mui/material/styles';
import { palette } from './palette';

let theme = createTheme({
  typography: {
    fontFamily: ['Rethink Sans'].join(','),
    button: {
      textTransform: 'none'
    },
    fontWeightBold: 700,
    fontWeightMedium: 600,
    fontWeightRegular: 500,
    fontWeightLight: 400,
    fontSize: 14,
    headerXl: {
      fontSize: '80px',
      lineHeight: '88px',
    },
    headerLg: {
      fontSize: '64px',
      lineHeight: '72px',
    },
    headerMd: {
      fontSize: '56px',
      lineHeight: '64px',
    },
    headerSm: {
      fontSize: '48px',
      lineHeight: '56px',
    },
    headerXs: {
      fontSize: '40px',
      lineHeight: '48px',
    },
    header2xs: {
      fontSize: '32px',
      lineHeight: '40px',
    },
    header3xs: {
      fontSize: '24px',
      lineHeight: '32px',
    },
    textXl: {
      fontSize: '20px',
      lineHeight: '30px',
    },
    textLg: {
      fontSize: '18px',
      lineHeight: '28px',
    },
    textMd: {
      fontSize: '16px',
      lineHeight: '24px',
    },
    textSm: {
      fontSize: '14px',
      lineHeight: '20px',
    },
    textXs: {
      fontSize: '12px',
      lineHeight: '18px',
    },
  },
  palette: {
    primary: palette.primary,
    error: palette.red,
    warning: palette.orange,
    success: palette.green,
    grey: palette.grey,
    green: palette.green,
    red: palette.red,
    orange: palette.orange,
    blue: palette.blue,
    others: palette.others,
  },
  components: {
    MuiButton: {
      defaultProps: {
        disableRipple: true,
      },
      styleOverrides: {
        root: {

        },
      },
    },
    MuiCheckbox: {
      defaultProps: {
        disableRipple: true
      }
    },
    MuiFormLabel: {
      styleOverrides: {
        asterisk: {
          color: palette.red[500]
        }
      }
    }
  },
});
theme = responsiveFontSizes(theme);

export default theme;
