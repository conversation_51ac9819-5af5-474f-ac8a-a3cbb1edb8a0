export interface Course {
  id: string;
  title: string;
  description: string;
  image: string;
  price: number;
  rating: number;
  lessons: number;
  duration: string;
  students: number;
  instructor: string;
  avatar: string;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  isFeatured: boolean;
  isEnrolled?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCourseRequest {
  title: string;
  description: string;
  image: string;
  price: number;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  isFeatured?: boolean;
}

export type UpdateCourseRequest = Partial<CreateCourseRequest>;

export type PaginationParams = object
