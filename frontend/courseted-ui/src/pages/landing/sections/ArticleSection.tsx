import Icon from '@/components/Icon';
import {
  Avatar,
  Card,
  CardContent,
  CardMedia,
  Grid,
  IconButton,
  Stack,
  Typography,
} from '@mui/material';
import { articles as mockArticles } from '@/data/mock_data';
import SectionLayout from '@/pages/landing/components/SectionLayout';
import SectionHeader from '@/pages/landing/components/SectionHeader';
import { useEffect, useState } from 'react';
import ArticleSectionSkeleton from '@/pages/landing/components/ArticleSectionSkeleton';

const ArticleSection = () => {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setArticles(mockArticles);
      setLoading(false);
    }, 2500);
    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return <ArticleSectionSkeleton />;
  }


  return (
    <SectionLayout py={{ xs: 5, sm: 10 }} maxWidth="xl">
      <SectionHeader title="articles" />

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card
            elevation={0}
            sx={{
              borderRadius: '24px',
              backgroundColor: 'grey.100',
              p: { xs: 2, sm: 3 },
            }}
          >
            <CardMedia
              component="img"
              image={articles[0].image}
              alt={articles[0].title}
              sx={{
                height: { xs: 200, sm: 300 },
                borderRadius: 4,
              }}
            />
            <CardContent>
              <Stack direction="row" justifyContent="space-between" alignItems="center">
                <Typography variant="header2xs" sx={{ fontWeight: 'fontWeightMedium' }}>
                  {articles[0].title}
                </Typography>
                <IconButton>
                  <Icon name="BookmarkAdd2" />
                </IconButton>
              </Stack>

              <Stack direction="row" alignItems="center" spacing={1} my={3}>
                {' '}
                <Avatar
                  sx={{
                    width: { xs: 30, sm: 36 },
                    height: { xs: 30, sm: 36 },
                  }}
                  src={articles[0].avatar}
                />
                <Typography sx={{ fontWeight: 'fontWeightMedium' }} variant="textLg">
                  {articles[0].author}
                </Typography>
                <Typography
                  sx={{ fontWeight: 'fontWeightLight' }}
                  variant="textSm"
                  color="grey.600"
                >
                  • Published on {articles[0].date}
                </Typography>
              </Stack>

              <Typography
                sx={{ fontWeight: 'fontWeightLight' }}
                variant="textMd"
                color="grey.700"
                mt={2}
              >
                {articles[0].description}
              </Typography>

              <Typography
                variant="textMd"
                mt={2}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  fontWeight: 'fontWeightBold',
                  color: 'primary.600',
                }}
              >
                Read full article <Icon name="ArrowRightGreen" sx={{ ml: 1 }} />
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {articles.length > 1 && (
          <Grid item xs={12} md={6}>
            <Stack spacing={{ xs: 1, sm: 2 }}>
              {articles.slice(1, 3).map(
                (
                  article
                ) => (
                  <Card
                    elevation={0}
                    key={article.id}
                    sx={{
                      borderRadius: '24px',
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      alignItems: 'center',
                      p: { xs: 2, sm: 3 },
                      gap: { xs: 2, sm: 3 },
                      backgroundColor: 'grey.100',
                    }}
                  >
                    <CardMedia
                      component="img"
                      image={article.image}
                      alt={article.title}
                      sx={{
                        height: { xs: 200, sm: 259 },
                        width: { xs: '100%', sm: 259 },
                        minWidth: { sm: 259 },
                        borderRadius: '16px',
                      }}
                    />
                    <CardContent sx={{ flex: 1, width: '100%' }}>
                      <Stack
                        direction="row"
                        alignItems="center"
                        justifyContent="space-between"
                        sx={{ mb: 2 }}
                      >
                        <Typography
                          variant="header3xs"
                          sx={{ fontWeight: 'fontWeightMedium' }}
                          color="grey.900"
                        >
                          {article.title}
                        </Typography>
                        <IconButton>
                          <Icon name="BookmarkAdd2" />
                        </IconButton>
                      </Stack>
                      <Stack direction="row" alignItems="center" spacing={2} my={2}>
                        <Avatar
                          sx={{
                            width: { xs: 28, sm: 32 },
                            height: { xs: 28, sm: 32 },
                          }}
                          src={article.avatar}
                        />
                        <Typography
                          variant="textLg"
                          color="grey.900"
                          sx={{ fontWeight: 'fontWeightMedium' }}
                        >
                          {article.author}
                        </Typography>
                      </Stack>
                      <Typography
                        variant="textMd"
                        color="grey.700"
                        sx={{ fontWeight: 'fontWeightLight' }}
                        my={1}
                      >
                        {article.description}
                      </Typography>
                      <Typography
                        variant="textMd"
                        mt={3}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer',
                          fontWeight: 'fontWeightRegular',
                          color: 'primary.600',
                        }}
                      >
                        Read full article <Icon name="ArrowRightGreen" sx={{ ml: 1 }} />
                      </Typography>
                    </CardContent>
                  </Card>
                )
              )}
            </Stack>
          </Grid>
        )}
      </Grid>
    </SectionLayout>
  );
};

export default ArticleSection;
