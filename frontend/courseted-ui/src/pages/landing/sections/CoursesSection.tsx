import {
  Grid,
  Grid2,
} from '@mui/material';
import { courses as mockCourses } from '@/data/mock_data';
import SectionLayout from '@/pages/landing/components/SectionLayout';
import { palette } from '@/theme/palette';
import SectionHeader from '@/pages/landing/components/SectionHeader';

import { useEffect, useState } from 'react';
import CourseCardSkeleton from '@/pages/landing/components/CourseCardSkeleton';
import CourseCard from '../components/CourseCard';

const CoursesSection = () => {
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setCourses(mockCourses);
      setLoading(false);
    }, 1500);
    return () => clearTimeout(timer);
  }, []);

  return (
    <SectionLayout py={{ xs: 5, sm: 10 }} maxWidth="xl" backgroundColor={palette.grey[200]}>
      <SectionHeader title="courses" />

      <Grid2 container spacing={4}>
        {loading
          ? Array.from(new Array(3)).map((_, index) => <CourseCardSkeleton key={index} />)
          : courses.map((course, idx) => {
            console.log(course?.title);

            return (
              <Grid
                item
                key={idx}
                display={'flex'}
                justifyContent={'center'}
                flex={1}
                >
                <CourseCard {...course} />
              </Grid>
            )
          })
        }
      </Grid2>
    </SectionLayout>
  );
};

export default CoursesSection;
