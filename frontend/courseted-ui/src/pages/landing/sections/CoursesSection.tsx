import { Grid2 } from '@mui/material';
import { courses as mockCourses } from '@/data/mock_data';
import SectionLayout from '@/pages/landing/components/SectionLayout';
import { palette } from '@/theme/palette';
import SectionHeader from '@/pages/landing/components/SectionHeader';

import CourseCard from '../components/CourseCard';

interface Course {
  id: number;
  title: string;
  description: string;
  image: string;
  category: string;
  rating: number;
  price: string;
  lessons: number;
  duration: string;
  students: number;
  instructor: string;
  time: string;
  avatar: string;
  iconName: string;
}

interface CoursesSectionProps {
  courses?: Course[];
}

const CoursesSection = ({ courses = mockCourses }: CoursesSectionProps) => {
  // Transform course data to match CourseCard interface
  const transformCourse = (course: Course) => ({
    ...course,
    id: course.id.toString(),
    price: parseFloat(course.price.replace('$', '')) || 0,
  });

  return (
    <SectionLayout py={{ xs: 5, sm: 10 }} maxWidth="xl" backgroundColor={palette.grey[200]}>
      <SectionHeader title="courses" />

      <Grid2 container spacing={4}>
        {courses.map((course, idx) => {
          return (
            <Grid2
              key={idx}
              size={{ xs: 12, sm: 6, md: 4 }}
              display={'flex'}
              justifyContent={'center'}
            >
              <CourseCard course={transformCourse(course)} />
            </Grid2>
          )
        })}
      </Grid2>
    </SectionLayout>
  );
};

export default CoursesSection;
