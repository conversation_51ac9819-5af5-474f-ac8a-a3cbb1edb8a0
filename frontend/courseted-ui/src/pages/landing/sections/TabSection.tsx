import {
  Avatar,
  Box,
  Card,
  CardContent,
  CardMedia,
  Divider,
  Stack,
  Tab,
  Tabs,
  Typography,
} from '@mui/material';
import { GreenSpan } from '@/pages/landing/components/GreenSpan';
import SectionLayout from '@/pages/landing/components/SectionLayout';
import theme from '@/theme';
import { courses as defaultCourses } from '@/data/mock_data';
import Icon from '@/components/Icon';
import React from 'react';
import { palette } from '@/theme/palette';
import { motion, AnimatePresence } from 'framer-motion';

interface Course {
  id: number;
  title: string;
  description: string;
  image: string;
  category: string;
  rating: number;
  price: string;
  lessons: number;
  duration: string;
  students: number;
  instructor: string;
  time: string;
  avatar: string;
  iconName: string;
}

interface TabSectionProps {
  courses?: Course[];
}

const TabSection = ({ courses = defaultCourses }: TabSectionProps) => {
  const [selectedTabIndex, setSelectedTabIndex] = React.useState(0);

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setSelectedTabIndex(newValue);
  };

  const currentCourse = courses[selectedTabIndex] || courses[0];
  return (
    <SectionLayout
      width={'100%'}
      py={{ xs: 5, sm: 10 }}
      maxWidth="xl"
      backgroundColor={'white'}
      height={'1064px'}
    >
      <Stack
        direction={'column'}
        justifyContent={'space-between'}
        alignItems={'center'}
        height={'100%'}
        gap={'64px'}
      >
        <Stack direction={'row'} alignItems={'center'} justifyContent={'center'} width={'100%'}>
          <Typography
            sx={{
              textAlign: 'center',
              color: 'grey.900',
              fontWeight: 'bold',
              fontSize: {
                xs: theme.typography.headerXs.fontSize,
                sm: theme.typography.headerSm.fontSize,
                md: theme.typography.headerMd.fontSize,
              },
              px: { xl: 45 },
            }}
          >
            Grow with us and be <GreenSpan fontWeight="bold">the best</GreenSpan> in the industry
          </Typography>
        </Stack>
        <Stack
          sx={{
            height: {
              md: '632px',
            },
            width: '100%',
          }}
          direction={'row'}
          spacing={'80px'}
        >
          <Box
            sx={{
              width: '100%',
              height: { md: '100%' },
              overflowY: { md: 'auto' },
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Tabs
              orientation="vertical"
              TabIndicatorProps={{
                children: (
                  <span
                    style={{
                      display: 'block',
                      height: '66px',
                      width: '6px',
                      backgroundColor: theme.palette.green[500],
                      borderRadius: '99px',
                      margin: 'auto 0',
                    }}
                  />
                ),
              }}
              sx={{
                width: '100%',
                '.MuiTabs-indicator': {
                  left: 0,
                  top: 0,
                  bottom: 0,
                  width: '6px',
                  display: 'flex',
                  alignItems: 'center',
                  backgroundColor: 'transparent',
                },
              }}
              value={selectedTabIndex}
              onChange={handleTabChange}
            >
              {courses.map((course, index) => (
                <Tab
                  key={course.id || index}
                  disableRipple
                  sx={{
                    p: 2.5,
                    mb: 2,
                    borderRadius: '16px',
                    alignItems: 'flex-start',
                    textAlign: 'left',
                    textTransform: 'none',
                    minWidth: '90%',
                    height: '190px',
                    backgroundColor: theme.palette.common.white,
                    boxShadow:
                      selectedTabIndex === index
                        ? `0 4px 12px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.02)`
                        : `0 1px 3px rgba(0, 0, 0, 0.04)`,
                    transform: selectedTabIndex === index ? ' translateX(12px)' : '',
                    transition: 'box-shadow 0.6s ease, transform 0.3s ease',
                  }}
                  label={
                    <Stack direction="column" spacing={2} alignItems="flex-start" width="100%">
                      <Stack direction={'row'} width={'100%'} gap={'24px'}>
                        <Stack
                          borderRadius={999}
                          alignItems={'center'}
                          justifyContent={'center'}
                          sx={{
                            bgcolor:
                              selectedTabIndex === index
                                ? palette.primary[500]
                                : theme.palette.grey[200],
                            color: selectedTabIndex === index ? 'white' : theme.palette.grey[200],
                            width: '64px',
                            height: '64px',
                            mt: 0.5,
                          }}
                        >
                          <Icon
                            name={course.iconName as any}
                            color={selectedTabIndex === index ? 'white' : theme.palette.grey[900]}
                          />
                        </Stack>
                        <Stack direction={'column'}>
                          <Typography
                            variant="textMd"
                            sx={{
                              color:
                                index === selectedTabIndex ? theme.palette.green[500] : 'grey.700',
                              fontWeight: 600,
                            }}
                          >
                            {'HEADER'}
                          </Typography>
                          <Typography
                            variant="header3xs"
                            sx={{
                              fontWeight: 600,
                              color:
                                index === selectedTabIndex ? theme.palette.green[600] : 'grey.900',
                              mt: 0.5,
                              mb: 0.5,
                            }}
                          >
                            {'Topic of Discussion'}
                          </Typography>
                        </Stack>
                      </Stack>
                      <Stack sx={{ flexGrow: 1, minWidth: 0 }}>
                        <Typography variant="textMd" color="grey.700">
                          {
                            'Having trained over 30,000 QA professionals, we share our knowledge and experience through a wide variety of testing courses.'
                          }{' '}
                        </Typography>
                      </Stack>
                    </Stack>
                  }
                />
              ))}
            </Tabs>
          </Box>
          <Stack
            height={'100%'}
            width={'100%'}
            justifyContent={'center'}
            alignItems={'center'}
            sx={{ background: theme.palette.grey[50] }}
            borderRadius="16px"
          >
            <AnimatePresence mode="wait">
              <motion.div
                key={currentCourse.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
              >
                <Card
                  elevation={0}
                  sx={{
                    borderRadius: '16px',
                    position: 'relative',
                    display: 'flex',
                    flexDirection: 'column',
                    flexGrow: 1,
                    width: '440px',
                    my: 3,
                  }}
                >
                  <CardMedia
                    sx={{ borderRadius: 6, p: 2 }}
                    component="img"
                    image={currentCourse.image}
                    alt={currentCourse.title}
                  />
                  <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 25,
                        right: 25,
                        backgroundColor: 'white',
                        p: 1,
                        borderRadius: '100px',
                        px: 2,
                        display: 'flex',
                        alignItems: 'center',
                      }}
                    >
                      <Typography variant="textMd" mr={0.5}>
                        {currentCourse.rating}
                      </Typography>
                      <Icon name="RatingStar" sx={{ ml: 0.5 }} />
                    </Box>

                    <Typography variant="header3xs" sx={{ fontWeight: 'fontWeightMedium' }}>
                      {currentCourse.title}
                    </Typography>

                    <Typography
                      variant="textMd"
                      color="grey.700"
                      sx={{ fontWeight: 'fontWeightLight' }}
                      mt={1}
                    >
                      {currentCourse.description}
                    </Typography>
                    <Divider sx={{ my: 1, opacity: 0.7 }} />
                    <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 2 }}>
                      <Stack direction="row" spacing={1}>
                        <Icon name="Lesson" />
                        <Typography variant="textSm" sx={{ fontWeight: 'fontWeightLight' }}>
                          {currentCourse.lessons} lessons
                        </Typography>
                      </Stack>
                      <Stack direction="row" spacing={1}>
                        <Icon name="Clock" />
                        <Typography variant="textMd" sx={{ fontWeight: 'fontWeightLight' }}>
                          {currentCourse.duration}
                        </Typography>
                      </Stack>

                      <Stack direction="row" spacing={1}>
                        <Icon name="Students" />
                        <Typography variant="textSm" sx={{ fontWeight: 'fontWeightLight' }}>
                          {currentCourse.students} students
                        </Typography>
                      </Stack>
                    </Stack>

                    <Divider sx={{ my: 2, opacity: 0.7 }} />
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Avatar sx={{ width: 40, height: 40 }} src={currentCourse.avatar} />
                        <Typography variant="textLg" sx={{ fontWeight: 'fontWeightMedium' }}>
                          {currentCourse.instructor}
                        </Typography>
                      </Stack>
                      <Typography
                        variant="header3xs"
                        sx={{ color: 'primary.600', fontWeight: 'fontWeightMedium' }}
                      >
                        {currentCourse.price}
                      </Typography>
                    </Stack>
                  </CardContent>
                </Card>
              </motion.div>
            </AnimatePresence>
          </Stack>
        </Stack>
      </Stack>
    </SectionLayout>
  );
};

export default TabSection;
