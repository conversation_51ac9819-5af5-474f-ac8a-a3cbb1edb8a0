import {
  Typo<PERSON>,
  Button,
  Stack,
  Paper,
  Avatar,
  Box,
  Grid,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import Image from 'next/image';
import avatarImage from '@/assets/images/avatar.webp';
import Icon from '@/components/Icon';
import SectionLayout from '@/pages/landing/components/SectionLayout';

interface HeroSectionProps {
  stats?: {
    totalStudents: number;
    totalCourses: number;
    totalInstructors: number;
    successRate: number;
  };
}

const HeroSection = ({ stats }: HeroSectionProps) => {
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up('md'), { noSsr: true });

  return (
    <SectionLayout maxWidth="xl" backgroundColor="grey.200" py={10}>
      <Grid container spacing={2} alignItems="center" justifyContent="center">
        <Grid item xs={12} md={isMdUp ? 7 : 12}>
          <Stack
            spacing={4}
            sx={{
              maxHeight: { md: '510px' },
              maxWidth: { md: '728px' },
              textAlign: { xs: 'center', md: 'left' },
            }}
          >
            <Typography
              variant="textXl"
              color="primary.600"
              sx={{
                gap: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: { xs: 'center', md: 'flex-start' },
                '&:hover': {
                  textDecoration: 'underline',
                  textDecorationColor: 'green.600',
                  textDecorationThickness: 2,
                  cursor: 'pointer',
                  textUnderlineOffset: 3,
                  textDecorationStyle: 'solid',
                },
              }}
            >
              <span>✨</span> Join our online community
            </Typography>
            <Typography
              variant={isMdUp ? 'headerLg' : 'headerSm'}
              sx={{
                fontWeight: 'bold',
              }}
            >
              Courseted is the best place for learning automation
            </Typography>
            <Typography variant="textLg" color="grey.700" sx={{ fontWeight: 'fontWeightLight' }}>
              Having trained over 30,000 QA professionals, we share our extensive knowledge and
              experience through a wide variety of testing courses. Our team is committed to
              assisting you with your goals, through instructor-led and coaching options.
            </Typography>
            <Stack direction="row" justifyContent={{ xs: 'center', md: 'flex-start' }} spacing={2}>
              <Button
                variant="contained"
                color="primary"
                sx={{
                  borderRadius: 100,
                  height: '60px',
                  width: '187px',
                  backgroundColor: 'black',
                  fontSize: 15,
                }}
                endIcon={
                  <Icon name="ArrayUpRight" sx={{ color: 'white', width: 16, height: 27 }} />
                }
              >
                Get started
              </Button>
            </Stack>
          </Stack>
        </Grid>

        {isMdUp && (
          <Grid item md={5} sx={{ position: 'relative' }}>
            <Stack direction={'row'} spacing={2} sx={{ justifyContent: 'end' }}>
              <Stack direction={'column'} spacing={2}>
                <Paper
                  elevation={0}
                  sx={{
                    borderRadius: '24px',
                    backgroundColor: 'white',
                    width: '264px',
                    height: '168px',
                    p: 3,
                    position: 'relative',
                  }}
                >
                  <Box position={'absolute'} right={15} top={15}>
                    <Icon name="SprayedDesignLanding" />
                  </Box>
                  <Typography variant="textXl" fontWeight="bold">
                    We have 40+{' '}
                  </Typography>
                  <Typography variant="textXl" fontWeight="bold">
                    Professional Teachers
                  </Typography>
                  <Stack direction="row" spacing={1} mt={1}>
                    {Array.from({ length: 4 }).map((_, index) => (
                      <Avatar key={index} sx={{ width: 44, height: 44 }} src={avatarImage} />
                    ))}
                  </Stack>
                </Paper>
                <Box
                  sx={{
                    width: '264px',
                    height: '360px',
                    borderRadius: '16px',
                    overflow: 'hidden',
                    position: 'relative',
                  }}
                >
                  <Image
                    src={'/images/landingCard.webp'}
                    alt="Online training session"
                    fill
                    style={{
                      objectFit: 'cover',
                    }}
                  />
                </Box>
              </Stack>

              <Stack direction="column" spacing={2}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    borderRadius: '24px',
                    backgroundColor: 'primary.600',
                    color: 'white',
                    textAlign: 'center',
                    width: '264px',
                    height: '264px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                  }}
                >
                  <Typography fontSize={64} sx={{ fontWeight: 'fontWeightMedium' }}>
                    100+
                  </Typography>
                  <Typography variant="textXl" sx={{ fontWeight: 'fontWeightMedium' }}>
                    professional courses
                  </Typography>
                </Paper>

                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    borderRadius: '24px',
                    backgroundColor: 'white',
                    width: '264px',
                    height: '264px',
                  }}
                >
                  <Stack
                    direction="row"
                    justifyContent={'space-between'}
                    alignItems="flex-start"
                    mb={3}
                  >
                    <Icon name="VideoCameraIconLanding" />
                    <Icon name="SprayedDesignLanding" />
                  </Stack>
                  <Stack spacing={1}>
                    <Typography variant="header3xs" fontWeight="bold">
                      Join our online training sessions
                    </Typography>
                    <Typography variant="textSm" color="grey.700">
                      Over 500 students trained successfully
                    </Typography>
                  </Stack>
                </Paper>
              </Stack>
            </Stack>
            <Icon
              name="HeroStarIcon"
              sx={{
                position: 'absolute',
                top: '20px',
                left: '0',
                transform: 'translateX(-50%)',
                display: { xs: 'none', lg: 'block' },
              }}
            />
            <Icon
              name="HeroStarIconBig"
              sx={{
                position: 'absolute',
                bottom: '-40px',
                right: '-40px',
                display: { xs: 'none', lg: 'block' },
              }}
            />
            <Icon
              name="HeroStarIconSmall"
              sx={{
                position: 'absolute',
                bottom: '20px',
                right: '-50px',
                opacity: 0.7,
                width: '20px',
                height: '20px',
                display: { xs: 'none', lg: 'block' },
              }}
            />
          </Grid>
        )}
      </Grid>
    </SectionLayout>
  );
};

export default HeroSection;
