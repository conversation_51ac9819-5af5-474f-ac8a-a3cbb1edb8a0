// 'use client';
import Icon from '@/components/Icon';
import { Box, CardContent, Typography, Avatar, Stack, CardMedia, Divider } from '@mui/material';
import { Card } from '@mui/material';

interface Course {
    id: string;
    title: string;
    description: string;
    image: string;
    category: string;
    rating: number;
    price: number;
    lessons: number;
    duration: string;
    students: number;
    instructor: string;
    time: string;
    avatar: string;
}

interface Props {
    course: Course;
}

export default function CourseCard({ course }: Props) {
    return (
        <Card
            elevation={0}
            sx={{
                borderRadius: '16px',
                position: 'relative',
                display: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                backgroundColor: '#f5f5f5',
            }}
        >
            <CardMedia
                sx={{ borderRadius: 6, p: 2 }}
                component="img"
                image={course?.image}
                alt={course?.title}
            />
            <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', pt: 0 }} >
                <Box
                    sx={{
                        position: 'absolute',
                        top: 25,
                        right: 25,
                        backgroundColor: 'white',
                        p: 1,
                        borderRadius: '100px',
                        px: 2,
                        display: 'flex',
                        alignItems: 'center',
                    }}
                >
                    <Typography mr={0.5}>
                        {course?.rating}
                    </Typography>
                    <Icon name="RatingStar" sx={{ ml: 0.5 }} />
                </Box>

                <Stack direction="row" justifyContent="space-between" alignItems="center">
                    {/* <Typography variant="header3xs" sx={{ fontWeight: 'fontWeightMedium' }}>
                        {course?.category}
                    </Typography> */}
                    <Box
                        // borderRadius={"10px"}
                        sx={{
                            backgroundColor: '#DCF7E7',
                            p: 1,
                            borderRadius: '100px',
                            px: 2,
                            display: 'flex',
                            alignItems: 'center',

                        }}
                    >
                        <Typography
                            variant="textSm"
                            color="#0F9D46"
                            sx={{ fontWeight: 'fontWeightMedium', fontSize: '14px !important' }}>
                            {course?.category}
                        </Typography>
                    </Box>
                    <Typography variant="textSm">{course?.time}</Typography>
                </Stack>

                <Typography variant="header3xs" sx={{ fontWeight: 'fontWeightMedium' }} mt={2}>
                    {course?.title}
                </Typography>

                <Typography
                    // variant="textMd"
                    color="grey.700"
                    sx={{ fontWeight: 'fontWeightLight' }}
                    mt={1}
                >
                    {course?.description}
                </Typography>
                <Divider sx={{ my: 1, opacity: 0.7 }} />
                <Stack direction="row" spacing={2} alignItems="center" sx={{ mt: 2 }}>
                    <Stack direction="row" spacing={1}>
                        <Icon name="Lesson" />
                        <Typography variant="textSm" sx={{ fontWeight: 'fontWeightLight' }}>
                            {course?.lessons} lessons
                        </Typography>
                    </Stack>
                    <Stack direction="row" spacing={1}>
                        <Icon name="Clock" />
                        <Typography sx={{ fontWeight: 'fontWeightLight' }}>
                            {course?.duration}
                        </Typography>
                    </Stack>

                    <Stack direction="row" spacing={1}>
                        <Icon name="Students" />
                        <Typography variant="textSm" sx={{ fontWeight: 'fontWeightLight' }}>
                            {course?.students} students
                        </Typography>
                    </Stack>
                </Stack>

                <Divider sx={{ my: 2, opacity: 0.7 }} />
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <Stack direction="row" spacing={1} alignItems="center">
                        <Avatar sx={{ width: 40, height: 40 }} src={course?.avatar} />
                        <Typography variant="textLg" sx={{ fontWeight: 'fontWeightMedium' }}>
                            {course?.instructor}
                        </Typography>
                    </Stack>
                    <Typography
                        variant="header3xs"
                        sx={{ color: 'primary.600', fontWeight: 'fontWeightMedium' }}
                    >
                        {course?.price}
                    </Typography>
                </Stack>
            </CardContent>
        </Card>
    );
};
