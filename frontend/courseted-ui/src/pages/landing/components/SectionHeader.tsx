import { Button, Stack, Typography } from '@mui/material';

import Icon from '@/components/Icon';
import { GreenSpan } from '@/pages/landing/components/GreenSpan';
type SectionHeaderProps = {
  title: string;
} & React.ComponentProps<typeof Stack>;

const SectionHeader = ({ title, ...props }: SectionHeaderProps) => {
  return (
    <Stack
      direction="row"
      justifyContent="space-between"
      alignItems="center"
      mb={8}
      sx={{
        flexDirection: { xs: 'column', sm: 'row' },
        gap: { xs: 2, sm: 0 },
      }}
      {...props}
    >
      <Typography
        variant="headerMd"
        sx={{
          textAlign: { xs: 'center', sm: 'left' },
          fontWeight: 'fontWeightBold',
        }}
      >
        Our recent <GreenSpan>{title}</GreenSpan>
      </Typography>
      <Button
        variant="contained"
        sx={{
          borderRadius: 100,
          backgroundColor: 'black',
          color: 'white',
          textTransform: 'none',
          px: { xs: 2, sm: 3 },
          py: { xs: 1, sm: 2 },
          fontSize: { xs: '14px', sm: '16px' },
          '&:hover': { backgroundColor: 'grey.800' },
        }}
        endIcon={
          <Icon
            name="ArrayUpRight"
            sx={{
              color: 'white',
              width: { xs: 12, sm: 16 },
              height: { xs: 20, sm: 27 },
            }}
          />
        }
      >
        View all {title}
      </Button>
    </Stack>
  );
};

export default SectionHeader;
