import { Box } from '@mui/material';
import { ReactNode } from 'react';

interface GreenSpanProps {
  children: ReactNode;
  fontWeight?: string;
}

export const GreenSpan = ({ children, fontWeight = 'semibold' }: GreenSpanProps) => {
  return (
    <Box
      component="span"
      sx={{
        color: 'primary.light',
        fontWeight: fontWeight,
      }}
    >
      {children}
    </Box>
  );
};

export default GreenSpan;
