import { Typo<PERSON>, <PERSON>ack, TextField, Button, Divider } from '@mui/material';
import CourseGroup from '../CourseGroup';
import { courses } from '@/pages/landing/mock_data';
import FilterListIcon from '@mui/icons-material/FilterList';
import SectionLayout from '@/pages/landing/components/SectionLayout';
import { palette } from '@/theme/palette';

const DegreeSection = () => {
    return (
        <SectionLayout
            py={{ xs: 5, sm: 10 }}
            maxWidth="xl"
        >
            <Stack direction="row" justifyContent="space-between" alignItems="center" mb={8}>
                <Typography variant="h4" fontWeight={600} fontSize={'40px !important'}>Find the right degree for you</Typography>
                <Stack direction="row" spacing={2}>
                    <TextField size="small" placeholder="Search" />
                    <Button variant="outlined" startIcon={<FilterListIcon />}>Filter</Button>
                </Stack>
            </Stack>
            
            {/* <PopularCoursesSlider /> */}
            <Divider sx={{ my: 8, backgroundColor: palette.grey[300] }} />
            <CourseGroup title="Popular courses" courses={courses} />
            <Divider sx={{ my: 8, backgroundColor: palette.grey[300] }} />
            <CourseGroup title="Popular courses" courses={courses} />
            <Divider sx={{ my: 8, backgroundColor: palette.grey[300] }} />
            <CourseGroup title="Popular courses" courses={courses} />
            <Divider sx={{ my: 8, backgroundColor: palette.grey[300] }} />
            <CourseGroup title="Popular courses" courses={courses} />
        </SectionLayout>
    );
};

export default DegreeSection;
