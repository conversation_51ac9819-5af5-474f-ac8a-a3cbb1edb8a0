import { Box, Typography, Grid, Grid2, Stack, IconButton } from '@mui/material';
import CourseCard from '../landing/components/CourseCard';
import CourseCardSkeleton from '../landing/components/CourseCardSkeleton';
import { useEffect, useState } from 'react';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';
import { palette } from '@/theme/palette';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/navigation';


const CourseGroup = ({ title, courses }) => {
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const timer = setTimeout(() => {
            setLoading(false);
        }, 1000);
        return () => clearTimeout(timer);
    }, []);

    return (
        <Box my={4} position={'relative'}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" mb={4}>
                <Typography variant="h5" fontWeight={600} mb={4} fontSize={'32px !important'}>{title}</Typography>

                <Stack direction="row" spacing={2} alignItems="center">
                    <IconButton
                        aria-label="arraw-left"
                        sx={{
                            backgroundColor: palette.grey[200],
                            position: 'relative',
                            '&:hover': {
                                backgroundColor: palette.grey[300],
                            },
                        }}
                        className="prev-btn"
                    >
                        <ChevronLeft style={{ color: palette.grey[900] }} />
                    </IconButton>
                    <IconButton
                        aria-label="arrow-right"
                        sx={{
                            backgroundColor: palette.grey[200],
                            position: 'relative',
                            '&:hover': {
                                backgroundColor: palette.grey[300],
                            },
                        }}
                        className="next-btn"
                    >
                        <ChevronRight style={{ color: palette.grey[900] }} />
                    </IconButton>
                </Stack>
            </Stack>
            <Grid2 container spacing={4}>
                {loading
                    ? Array.from(new Array(3)).map((_, index) => <CourseCardSkeleton key={index} />)
                    :
                    <Swiper
                        modules={[Navigation]}
                        spaceBetween={20}
                        slidesPerView={1.1}
                        breakpoints={{
                            768: { slidesPerView: 2 },
                            1024: { slidesPerView: 3 },
                        }}
                        navigation={{
                            prevEl: '.prev-btn',
                            nextEl: '.next-btn',
                        }}
                        style={{ paddingBottom: '40px' }}
                    >
                        {
                            courses.map((course, idx) => {
                                return (
                                    <SwiperSlide key={idx}>
                                        <Grid
                                            item
                                            key={idx}
                                            display={'flex'}
                                            justifyContent={'center'}
                                            flex={1}
                                        >
                                            <CourseCard {...course} />
                                        </Grid>

                                    </SwiperSlide>
                                )
                            })
                        }
                    </Swiper>
                }
            </Grid2>
        </Box>
    );
};

export default CourseGroup;
