import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from './baseQuery';

const getApiBaseUrl = () => {
  if (typeof window === 'undefined') {
    // Server-side: use internal URL or default
    return process.env.API_URL || 'http://localhost:8000';
  }
  // Client-side: use public env var or default
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
};

export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery: axiosBaseQuery({
    baseUrl: getApiBaseUrl(),
  }),
  tagTypes: ['User', 'Course', 'Article', 'Auth', 'Country'],
  endpoints: () => ({}),
});
