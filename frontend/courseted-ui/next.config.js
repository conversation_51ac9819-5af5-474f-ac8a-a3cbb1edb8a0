/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // Performance optimizations
  poweredByHeader: false,
  compress: true,
  distDir: '.next',
  // Compiler optimizations
  compiler: {
    emotion: true,
    removeConsole: process.env.NODE_ENV === 'production',
    reactRemoveProperties: process.env.NODE_ENV === 'production',
  },

  // Environment variables
  env: {
    API_URL: process.env.API_URL || 'http://localhost/api/rest',
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || process.env.API_URL || 'http://localhost:8000',
    NEXT_PUBLIC_ENV: process.env.NODE_ENV,
  },

  // Experimental features for performance
  experimental: {
    optimizeCss: true,
    optimizeServerReact: true,
    webVitalsAttribution: ['CLS', 'LCP'],
  },

  async redirects() {
    return [
      // Redirect /login to /auth/login for backward compatibility
      {
        source: '/login',
        destination: '/auth/login',
        permanent: true,
      },
      {
        source: '/register',
        destination: '/auth/register',
        permanent: true,
      },
      {
        source: '/forgot-password',
        destination: '/auth/forgot-password',
        permanent: true,
      },
    ]
  },

  async rewrites() {
    return [
      // API proxy for development
      ...(process.env.NODE_ENV === 'development' ? [
        {
          source: '/api/:path*',
          destination: `${process.env.API_URL || 'http://localhost/api/rest'}/:path*`,
        }
      ] : []),
    ]
  },

  // Source maps configuration
  productionBrowserSourceMaps: false,
  generateBuildId: async () => {
    // Use git commit hash for build ID in production
    if (process.env.NODE_ENV === 'production') {
      try {
        const { execSync } = require('child_process');
        return execSync('git rev-parse HEAD').toString().trim();
      } catch {
        return 'build-' + Date.now();
      }
    }
    return null;
  },

  // Webpack configuration for optimization and compatibility
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Exclude test files from build
    config.module.rules.push({
      test: /\.(test|spec)\.(js|jsx|ts|tsx)$/,
      loader: 'ignore-loader',
    });

    // Exclude test directories
    config.resolve.alias = {
      ...config.resolve.alias,
      '@/*': './src',
      '@app': './app',
      '@src': './src',
      '@theme': './src/theme',
      '@routes': './src/routes',
      '@components': './src/components',
      '@features': './src/features',
      '@api': './src/api',
      '@utils': './src/utils',
      '@hooks': './src/hooks',
      '@pages': './src/pages',
      '@assets': './src/assets',
      '@types': './src/types',
    }

    // Ignore test and story files in webpack
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /\.(test|spec|stories)\.(js|jsx|ts|tsx)$/,
      }),
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/test$/,
      }),
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/tests$/,
      }),
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/__tests__$/,
      }),
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/__mocks__$/,
      })
    );

    // Production optimizations
    if (!dev && !isServer) {
      // Bundle analyzer in production (optional)
      if (process.env.ANALYZE === 'true') {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins.push(
          new BundleAnalyzerPlugin({
            analyzerMode: 'static',
            openAnalyzer: false,
            reportFilename: './analyze/client.html'
          })
        );
      }

      // Optimize chunks
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10,
            },
            mui: {
              test: /[\\/]node_modules[\\/]@mui[\\/]/,
              name: 'mui',
              chunks: 'all',
              priority: 20,
            },
            react: {
              test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
              name: 'react',
              chunks: 'all',
              priority: 20,
            },
          },
        },
      };
    }

    // Development optimizations
    if (dev) {
      // Faster builds in development
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
      };
    }

    return config
  },

  // Image optimization
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'https',
        hostname: '*.courseted.com',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      },
    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },

  // TypeScript configuration
  typescript: {
    ignoreBuildErrors: true,
  },

  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: true,
    dirs: ['src', 'pages'],
  },

  // Output configuration for production
  ...(process.env.NODE_ENV === 'production' && {
    output: 'standalone',
    trailingSlash: false,
  }),
}

export default nextConfig
