"use client";

import { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Switch,
} from '@mui/material';
import { Notifications, Security, Palette, Language } from '@mui/icons-material';
import WithAuth from '../components/WithAuth';
import MainLayout from '../components/MainLayout';

const SettingsPage = () => {
  const [settings, setSettings] = useState({
    notifications: true,
    darkMode: false,
    twoFactor: false,
    language: 'en',
  });

  const handleToggle = (setting: keyof typeof settings) => {
    setSettings(prev => ({
      ...prev,
      [setting]: !prev[setting],
    }));
  };

  return (
    <MainLayout>
      <WithAuth>
        <Box sx={{ p: 3 }}>
          <Typography variant="h4" gutterBottom>
            Settings
          </Typography>
          <Paper sx={{ mt: 2 }}>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Notifications />
                </ListItemIcon>
                <ListItemText primary="Notifications" secondary="Enable push notifications" />
                <Switch
                  edge="end"
                  checked={settings.notifications}
                  onChange={() => handleToggle('notifications')}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <Palette />
                </ListItemIcon>
                <ListItemText primary="Dark Mode" secondary="Toggle dark/light theme" />
                <Switch
                  edge="end"
                  checked={settings.darkMode}
                  onChange={() => handleToggle('darkMode')}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <Security />
                </ListItemIcon>
                <ListItemText
                  primary="Two-Factor Authentication"
                  secondary="Enable 2FA for better security"
                />
                <Switch
                  edge="end"
                  checked={settings.twoFactor}
                  onChange={() => handleToggle('twoFactor')}
                />
              </ListItem>
              <Divider />
              <ListItem>
                <ListItemIcon>
                  <Language />
                </ListItemIcon>
                <ListItemText primary="Language" secondary="Current: English" />
              </ListItem>
            </List>
          </Paper>
        </Box>
      </WithAuth>
    </MainLayout>
  );
};

export default SettingsPage;
