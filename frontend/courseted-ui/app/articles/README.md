# Articles Section with ISR (Incremental Static Regeneration)

This directory contains the articles section of the CourseTed application, implemented with Next.js 15 App Router and ISR for optimal performance.

## Features

### 🚀 Incremental Static Regeneration (ISR)
- **Main Articles Page**: Revalidates every 60 seconds
- **Individual Article Pages**: Revalidates every 5 minutes (300 seconds)
- **Static Generation**: Pre-generates top 10 articles at build time
- **On-demand Revalidation**: Supports manual cache invalidation

### 📱 User Experience
- **Loading States**: Custom loading component with spinner
- **Error Handling**: Comprehensive error boundary with retry functionality
- **404 Pages**: Custom not-found page for missing articles
- **Clickable Cards**: Hover effects and navigation to individual articles

### 🎨 Components

#### Main Components
- `page.tsx` - Main articles listing page with ISR
- `[id]/page.tsx` - Individual article page with dynamic routing
- `loading.tsx` - Loading state component
- `error.tsx` - Error boundary component
- `[id]/not-found.tsx` - 404 page for missing articles

#### Sections
- `sections/DegreeSection.tsx` - Main content section with course listings
- `sections/SectionHeader.tsx` - Header section
- `sections/SwiperSection.tsx` - Swiper carousel section

#### Custom Components
- `CourseGroup.tsx` - Course grouping component with Grid2 layout
- `components/ClickableCourseCard.tsx` - Interactive course cards with navigation

### 🔧 Technical Implementation

#### ISR Configuration
```typescript
// Main articles page - revalidate every 60 seconds
export const revalidate = 60;

// Individual article pages - revalidate every 5 minutes
export const revalidate = 300;
```

#### Data Fetching
```typescript
// Fetch with ISR caching
const res = await fetch('https://your-api.com/articles', {
  next: { revalidate: 60 }
});
```

#### Static Params Generation
```typescript
export async function generateStaticParams() {
  // Pre-generate top 10 articles at build time
  return articles.slice(0, 10).map((article) => ({
    id: article.id.toString(),
  }));
}
```

### 🎯 Performance Benefits

1. **Fast Initial Load**: Pre-generated static pages
2. **Fresh Content**: Automatic revalidation keeps content up-to-date
3. **Reduced Server Load**: Cached responses reduce API calls
4. **SEO Optimized**: Static generation improves search engine indexing
5. **Scalable**: Handles traffic spikes with cached content

### 🛠 Development

#### Running Locally
```bash
npm run dev
```

#### Building for Production
```bash
npm run build
npm start
```

#### Type Safety
All components are fully typed with TypeScript interfaces for:
- Course data structure
- Component props
- API responses
- Error handling

### 📊 Monitoring

The ISR implementation includes:
- Error logging for failed data fetches
- Fallback data for API failures
- Performance monitoring through Next.js analytics
- Cache hit/miss tracking

### 🔄 Cache Management

#### Manual Revalidation
```typescript
// Trigger revalidation programmatically
await revalidatePath('/articles');
await revalidateTag('articles');
```

#### Cache Strategies
- **Articles List**: Short cache (60s) for fresh content
- **Individual Articles**: Longer cache (5min) for stable content
- **Static Assets**: Long-term caching for images and styles

### 🚦 Error Handling

1. **Network Errors**: Graceful fallback to cached content
2. **API Failures**: Default to mock data with error logging
3. **Missing Content**: Custom 404 pages with navigation
4. **Runtime Errors**: Error boundaries with retry functionality

This implementation provides a robust, performant, and user-friendly articles section that scales well and provides excellent user experience.
