// "use client";
import { Box, Typography, Stack, IconButton, Grid } from '@mui/material';
import { palette } from '@/theme/palette';
import ClickableCourseCard from './components/ClickableCourseCard';

interface Course {
    id: number;
    title: string;
    description: string;
    image: string;
    category: string;
    rating: number;
    price: string;
    lessons: number;
    duration: string;
    students: number;
    instructor: string;
    time: string;
    avatar: string;
    iconName: string;
}

interface CourseGroupProps {
    title: string;
    courses: Course[];
}

const CourseGroup = ({ title, courses }: CourseGroupProps) => {
    // Transform course data to match CourseCard interface
    const transformCourse = (course: Course) => ({
        ...course,
        id: course.id.toString(),
        price: parseFloat(course.price.replace('$', '')) || 0,
    });

    return (
        <Box my={4} position={'relative'}>
            <Stack direction="row" justifyContent="space-between" alignItems="center" mb={4}>
                <Typography variant="h5" fontWeight={600} mb={4} fontSize={'32px !important'}>{title}</Typography>

                <Stack direction="row" spacing={2} alignItems="center">
                    <IconButton
                        aria-label="arraw-left"
                        sx={{
                            backgroundColor: palette.grey[200],
                            position: 'relative',
                            '&:hover': {
                                backgroundColor: palette.grey[300],
                            },
                        }}
                        className="prev-btn"
                    >
                        {/* <ChevronLeft style={{ color: palette.grey[900] }} /> */}
                    </IconButton>
                    <IconButton
                        aria-label="arrow-right"
                        sx={{
                            backgroundColor: palette.grey[200],
                            position: 'relative',
                            '&:hover': {
                                backgroundColor: palette.grey[300],
                            },
                        }}
                        className="next-btn"
                    >
                        {/* <ChevronRight style={{ color: palette.grey[900] }} /> */}
                    </IconButton>
                </Stack>
            </Stack>
            <Grid container spacing={4}>
                {
                    courses?.map((course, idx) => {
                        // console.log('CourseGroup course:::', course);
                        return (
                            // <Grid
                            //     key={idx}
                            //     size={{ xs: 12, sm: 6, md: 4 }}
                            //     display={'flex'}
                            //     justifyContent={'center'}
                            // >
                                <ClickableCourseCard key={idx} course={transformCourse(course)} />
                            // </Grid>
                        )
                    })
                }
            </Grid>
        </Box>
    );
};

export default CourseGroup;
