'use client';

import { Box } from '@mui/material';
import { useRouter } from 'next/navigation';
import CourseCard from '@/pages/landing/components/CourseCard';

interface Course {
    id: string;
    title: string;
    description: string;
    image: string;
    category: string;
    rating: number;
    price: string; // Changed to string to match CourseCard expectation
    lessons: number;
    duration: string;
    students: number;
    instructor: string;
    time: string;
    avatar: string;
}

interface ClickableCourseCardProps {
    course: Course;
}

export default function ClickableCourseCard({ course }: ClickableCourseCardProps) {
    const router = useRouter();

    const handleClick = () => {
        router.push(`/articles/${course.id}`);
    };

    return (
        <Box
            onClick={handleClick}
            sx={{
                cursor: 'pointer',
                transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                '&:hover': {
                    transform: 'translateY(-4px)',
                    '& .MuiCard-root': {
                        boxShadow: '0 8px 25px rgba(0,0,0,0.12)',
                    },
                },
                '&:active': {
                    transform: 'translateY(-2px)',
                },
            }}
        >
            <CourseCard course={course} />
        </Box>
    );
}
