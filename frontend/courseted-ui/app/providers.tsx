"use client";

import type { ReactNode } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Provider } from 'react-redux';
import theme from '@/theme';
import { store } from '@/app/store';
import { useIsClient } from '@/hooks/useClient';
import ErrorBoundary from './components/ErrorBoundary';

interface ProvidersProps {
  children: ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
  const isClient = useIsClient();

  return (
    <ErrorBoundary>
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          {/* Prevent hydration mismatch by ensuring consistent rendering */}
          <div suppressHydrationWarning>
            {isClient ? children : children}
          </div>
        </ThemeProvider>
      </Provider>
    </ErrorBoundary>
  );
}
