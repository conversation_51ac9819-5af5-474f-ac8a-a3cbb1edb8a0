import { FC } from 'react';
import { Box, Typography, Button, Container } from '@mui/material';
import { Lock, ArrowBack } from '@mui/icons-material';
import Link from 'next/link';
import MainLayout from '../components/MainLayout';

// Generate metadata for the unauthorized page
export async function generateMetadata() {
  return {
    title: 'Unauthorized - CourseTed',
    description: 'You do not have permission to access this page.',
    robots: 'noindex, nofollow',
  };
}

const UnauthorizedPage: FC = () => {
  return (
    <MainLayout>
      <Container maxWidth="sm">
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="60vh"
          textAlign="center"
          gap={3}
        >
          <Lock color="error" sx={{ fontSize: 80 }} />
          
          <Typography variant="h3" component="h1" fontWeight={600}>
            Access Denied
          </Typography>
          
          <Typography variant="h6" color="text.secondary" maxWidth={400}>
            You don't have permission to access this page. Please contact your administrator if you believe this is an error.
          </Typography>
          
          <Box display="flex" gap={2} flexDirection={{ xs: 'column', sm: 'row' }}>
            <Button
              component={Link}
              href="/"
              variant="contained"
              startIcon={<ArrowBack />}
              size="large"
            >
              Go Home
            </Button>
            <Button
              component={Link}
              href="/contact"
              variant="outlined"
              size="large"
            >
              Contact Support
            </Button>
          </Box>
        </Box>
      </Container>
    </MainLayout>
  );
};

export default UnauthorizedPage;
