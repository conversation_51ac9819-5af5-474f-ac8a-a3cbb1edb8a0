:root {
  --font-rethink-sans: 'Rethink Sans', system-ui, sans-serif;
  --font-public-sans: 'Public Sans', system-ui, sans-serif;

  /* Light theme colors */
  --background: #ffffff;
  --foreground: #0f0f23;
  --primary: #4285f4;
  --primary-foreground: #ffffff;
  --secondary: #f1f3f4;
  --secondary-foreground: #5f6368;
  --muted: #f8f9fa;
  --muted-foreground: #6b7280;
  --border: #e5e7eb;
  --input: #ffffff;
  --ring: #4285f4;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f0f23;
    --foreground: #ffffff;
    --primary: #4285f4;
    --primary-foreground: #ffffff;
    --secondary: #1f2937;
    --secondary-foreground: #d1d5db;
    --muted: #374151;
    --muted-foreground: #9ca3af;
    --border: #374151;
    --input: #1f2937;
    --ring: #4285f4;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-public-sans);
  line-height: 1.6;
  color: var(--foreground);
  background-color: var(--background);
}

a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  font-family: inherit;
}

input,
textarea {
  font-family: inherit;
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}
