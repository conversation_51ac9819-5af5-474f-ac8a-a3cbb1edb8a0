'use client';
import { Box, Typography, Grid, Paper } from '@mui/material';
import WithAuth from '../components/WithAuth';
import MainLayout from '../components/MainLayout';

const DashboardPage = () => {

  return (
    <MainLayout>
      <WithAuth>
        <Box sx={{ p: 3 }}>
          <Typography variant="h4" gutterBottom>
            Dashboard
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6} lg={4}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Welcome,
                </Typography>
                <Typography variant="body1">This is your dashboard overview</Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6} lg={4}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Statistics
                </Typography>
                <Typography variant="body1">Your activity overview will appear here</Typography>
              </Paper>
            </Grid>
          </Grid>
        </Box>
      </WithAuth>
    </MainLayout>
  );
};

export default DashboardPage;
