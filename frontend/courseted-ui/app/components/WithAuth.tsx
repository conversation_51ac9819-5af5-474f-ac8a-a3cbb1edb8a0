
'use client';

import { FC, ReactNode, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/redux/useAuth';
import { Box, CircularProgress, Typography } from '@mui/material';

interface WithAuthProps {
  children: ReactNode;
  redirectTo?: string;
  fallback?: ReactNode;
}

const WithAuth: FC<WithAuthProps> = ({
  children,
  redirectTo = '/auth/login',
  fallback
}) => {
  const router = useRouter();
  const { isAuthenticated, isLoadingUser } = useAuth();

  useEffect(() => {
    // Only redirect if we're sure the user is not authenticated
    // and not currently loading user data
    if (!isLoadingUser && !isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoadingUser, router, redirectTo]);

  // Show loading state while checking authentication
  if (isLoadingUser) {
    return fallback || (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          gap: 2,
        }}
      >
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary">
          Verifying authentication...
        </Typography>
      </Box>
    );
  }

  // Don't render children if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  // User is authenticated, render children
  return <>{children}</>;
};

export default WithAuth;
