'use client';
import { Box } from '@mui/material';
import Header from '@/components/common/Header';
import Footer from '@/components/common/Footer';
import { useHydrated } from '@/hooks/useClient';

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const hydrated = useHydrated();

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <Header />
      <Box component="main" sx={{ flex: 1 }} suppressHydrationWarning>
        {hydrated ? children : children}
      </Box>
      <Footer />
    </Box>
  );
}
