import { FC, ReactNode } from 'react';
import WithAuth from './WithAuth';

// Higher-order component version for wrapping entire components
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    redirectTo?: string;
    fallback?: ReactNode;
  }
) => {
  const WrappedComponent: FC<P> = (props) => {
    return (
      <WithAuth redirectTo={options?.redirectTo} fallback={options?.fallback}>
        <Component {...props} />
      </WithAuth>
    );
  };

  WrappedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  return WrappedComponent;
};
