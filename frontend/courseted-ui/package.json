{"name": "courseted-ui", "private": true, "version": "1.0.0", "type": "module", "scripts": {"prepare": "husky install", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:watch": "node --experimental-vm-modules node_modules/jest/bin/jest.js --watch", "test:coverage": "node --experimental-vm-modules node_modules/jest/bin/jest.js --coverage", "dev": "next dev", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,json,md}\" \"pages/**/*.{ts,tsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,json,md}\" \"pages/**/*.{ts,tsx,json,md}\"", "check:all": "npm run type-check && npm run lint && npm run format:check", "clean": "rm -rf .next && rm -rf out", "build:prod": "npm run clean && npm run check:all && next build", "export": "next export", "postbuild": "echo 'Build completed successfully!'", "legacy:dev": "vite --mode development", "legacy:build:dev": "vite build --mode development", "legacy:build:prod": "vite build --mode production", "legacy:preview:dev": "vite preview --mode development", "legacy:preview:prod": "vite preview --mode production"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.8", "@mui/material": "^6.4.8", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.8.4", "framer-motion": "^12.5.0", "next": "^15.1.6", "npx": "^10.2.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.4.0", "swiper": "^11.2.10", "web-vitals": "^4.2.4", "yup": "^1.6.1"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/js": "^9.21.0", "@next/eslint-plugin-next": "^15.1.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.5.4", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^8.27.0", "@typescript-eslint/parser": "^8.27.0", "@vitejs/plugin-react": "^4.3.4", "critters": "^0.0.23", "eslint": "^9.23.0", "eslint-config-next": "^15.1.6", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.0", "prettier": "^3.5.3", "ts-jest": "^29.2.6", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "webpack-bundle-analyzer": "^4.10.2"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}