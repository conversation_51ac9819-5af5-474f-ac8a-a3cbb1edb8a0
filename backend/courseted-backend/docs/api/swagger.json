{"openapi": "3.0.0", "paths": {"/v1/users": {"get": {"description": "Get all users with search, pagination, and sorting capabilities", "operationId": "getUsers", "parameters": [{"name": "search", "required": false, "in": "query", "description": "Search users by email or phone number", "schema": {"example": "john", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number (1-based)", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "size", "required": false, "in": "query", "description": "Alias for limit - items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field (email, phoneNumber, createdAt, updatedAt, id, role)", "schema": {"example": "createdAt", "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get All Users with Filtering", "tags": ["Users", "Users"]}}, "/v1/users/instructors": {"get": {"description": "Get instructors with search, pagination, and sorting", "operationId": "getInstructors", "parameters": [{"name": "search", "required": false, "in": "query", "description": "Search instructors by email or phone number", "schema": {"example": "john", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number (1-based)", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "size", "required": false, "in": "query", "description": "Alias for limit - items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field (email, phoneNumber, createdAt, updatedAt, id)", "schema": {"example": "createdAt", "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get instructors with filtering", "tags": ["Users", "Users"]}}, "/v1/users/students": {"get": {"description": "Get students with search, pagination, and sorting", "operationId": "getStudents", "parameters": [{"name": "search", "required": false, "in": "query", "description": "Search students by email or phone number", "schema": {"example": "john", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number (1-based)", "schema": {"minimum": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "size", "required": false, "in": "query", "description": "Alias for limit - items per page (default: 10, max: 100)", "schema": {"minimum": 1, "maximum": 100, "example": 10, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Sort by field (email, phoneNumber, createdAt, updatedAt, id)", "schema": {"example": "createdAt", "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Sort order", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get students with filtering", "tags": ["Users", "Users"]}}, "/v1/users/profile": {"get": {"description": "Get profile", "operationId": "getProfile", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get profile", "tags": ["Users", "Users"]}, "post": {"description": "Storing profile", "operationId": "storeProfile", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "Store profile", "tags": ["Users", "Users"]}}, "/v1/auth/login": {"post": {"description": "Login with email and password", "operationId": "login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "<PERSON><PERSON>", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/register": {"post": {"description": "Register", "operationId": "register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "Register", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/profile": {"get": {"description": "profile", "operationId": "profile", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Profile", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/google": {"get": {"description": "Google login", "operationId": "google", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Google Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/google/callback": {"get": {"description": "Login with email and password", "operationId": "googleCallback", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Google Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/facebook": {"get": {"description": "Login with email and password", "operationId": "facebookAuth", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Facebook Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/facebook/callback": {"get": {"description": "Facebook login", "operationId": "facebookCallback", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Facebook Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/linkedin": {"get": {"description": "Facebook login", "operationId": "linkedinAuth", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Facebook Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/linkedin/callback": {"get": {"description": "linkedin login", "operationId": "linkedinAuthCallback", "parameters": [], "responses": {"200": {"description": ""}}, "summary": "Facebook Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/verify-otp": {"post": {"description": "verifyOtp", "operationId": "verifyOtp", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyOtpDto"}}}}, "responses": {"201": {"description": ""}}, "summary": "Facebook Callback", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/auth/context": {"get": {"description": "Get current user context data for React client", "operationId": "context", "parameters": [{"name": "Authorization", "in": "header", "description": "Bearer JWT access token", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "summary": "Get User Context", "tags": ["<PERSON><PERSON>", "<PERSON><PERSON>"]}}, "/v1/countries": {"get": {"description": "Retrieve all countries with their codes, names, phone codes, and flags", "operationId": "CountryController_findAll_v1", "parameters": [], "responses": {"200": {"description": "List of all countries", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Country"}}}}}}, "summary": "Get all countries", "tags": ["Countries"]}}, "/v1/countries/{code}": {"get": {"description": "Retrieve a specific country by its ISO 2-letter code", "operationId": "CountryController_findByCode_v1", "parameters": [{"name": "code", "required": true, "in": "path", "description": "ISO 2-letter country code (e.g., US, UK, CA)", "schema": {"example": "US", "type": "string"}}], "responses": {"200": {"description": "Country found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Country"}}}}, "404": {"description": "Country not found"}}, "summary": "Get country by code", "tags": ["Countries"]}}, "/v1/sms/send": {"post": {"operationId": "SmsController_sendSms_v1", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmsDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Sms"]}}, "/v1/sms/bulk": {"post": {"operationId": "SmsController_sendBulkSms_v1", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Sms"]}}}, "info": {"title": "Courseted API", "description": "Courseted API Description", "version": "1.0.0", "contact": {}}, "tags": [], "servers": [{"url": "http://localhost:8000"}], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"ProfileDto": {"type": "object", "properties": {"firstName": {"type": "string", "description": "The first name of the user", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "The last name of the user", "example": "<PERSON><PERSON>"}, "profilePicture": {"type": "string", "description": "URL to the user's profile picture", "example": "https://courseted.com/images/profile.jpg"}}, "required": ["firstName", "lastName"]}, "LoginDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>", "description": "The email of the user"}, "password": {"type": "string", "example": "password123", "description": "The password of the user", "minLength": 6}}, "required": ["email", "password"]}, "RegisterDto": {"type": "object", "properties": {"email": {"type": "string", "description": "The email address of the user", "example": "<EMAIL>"}, "password": {"type": "string", "description": "The password of the user", "example": "password123", "minLength": 6}, "phoneNumber": {"type": "string", "description": "The phone number of the user", "example": "123456789"}, "countryId": {"type": "number", "description": "The country ID of the user", "example": "1"}}, "required": ["email", "password", "phoneNumber", "countryId"]}, "VerifyOtpDto": {"type": "object", "properties": {"code": {"type": "string", "description": "Code for the user", "example": "123567"}, "phoneNumber": {"type": "string", "description": "The phone number of the user", "example": "123456789"}}, "required": ["code", "phoneNumber"]}, "Country": {"type": "object", "properties": {"createdAt": {"format": "date-time", "type": "string", "description": "The date when the profile was created", "example": "2023-01-01T00:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "The date when the profile was last updated", "example": "2023-01-02T00:00:00Z"}, "id": {"type": "number", "description": "The unique identifier of the country", "example": "1"}, "name": {"type": "string", "description": "The name of the country", "example": "United States"}, "code": {"type": "string", "description": "The ISO 3166-1 alpha-2 country code", "example": "US"}, "iso3": {"type": "string", "description": "The ISO 3166-1 alpha-3 country code", "example": "USA"}, "phoneCode": {"type": "string", "description": "The international dialing code", "example": "+1"}, "flag": {"type": "string", "description": "The emoji flag of the country", "example": "🇺🇸"}, "profiles": {"description": "Profiles associated with this country", "type": "array", "items": {"type": "Profile"}}}, "required": ["createdAt", "updatedAt", "id", "name", "code", "iso3", "phoneCode", "profiles"]}, "SmsDto": {"type": "object", "properties": {"password": {"type": "string", "example": "<EMAIL>", "description": "The email of the user"}, "to": {"type": "string", "example": "<EMAIL>", "description": "The email of the user"}, "body": {"type": "string", "example": "<EMAIL>", "description": "The email of the user"}, "from": {"type": "string", "example": "<EMAIL>", "description": "The email of the user"}, "senderId": {"type": "number", "example": "<EMAIL>", "description": "The email of the user"}}, "required": ["password", "to", "body", "from", "senderId"]}}}, "externalDocs": {"description": "More Information", "url": ""}}