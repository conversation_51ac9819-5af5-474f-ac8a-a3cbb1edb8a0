{"item": [{"id": "a9535f10-e015-4b7c-9903-eafd3f0a3369", "name": "Users", "item": [{"id": "87046bc0-ae9c-4146-a709-fc0028a55a59", "name": "GET /v1/users", "request": {"description": {"content": "Get All Users with Filtering", "type": "text/plain"}, "url": {"path": ["v1", "users"], "host": ["{{baseUrl}}"], "query": [{"description": {"content": "Search users by email or phone number", "type": "text/plain"}, "key": "search", "value": "john"}, {"description": {"content": "Page number (1-based)", "type": "text/plain"}, "key": "page", "value": "1"}, {"description": {"content": "Items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "limit", "value": "10"}, {"description": {"content": "Alias for limit - items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "size", "value": "10"}, {"description": {"content": "Sort by field (email, phoneNumber, createdAt, updatedAt, id, role)", "type": "text/plain"}, "key": "sortBy", "value": "createdAt"}, {"description": {"content": "Sort order", "type": "text/plain"}, "key": "sortOrder", "value": "value"}], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "3736ad45-6e4c-4cf2-9542-b1a5219cd912", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "4f8ab168-fbc0-4176-8065-9ea49625382a", "name": "GET /v1/users/instructors", "request": {"description": {"content": "Get instructors with filtering", "type": "text/plain"}, "url": {"path": ["v1", "users", "instructors"], "host": ["{{baseUrl}}"], "query": [{"description": {"content": "Search instructors by email or phone number", "type": "text/plain"}, "key": "search", "value": "john"}, {"description": {"content": "Page number (1-based)", "type": "text/plain"}, "key": "page", "value": "1"}, {"description": {"content": "Items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "limit", "value": "10"}, {"description": {"content": "Alias for limit - items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "size", "value": "10"}, {"description": {"content": "Sort by field (email, phoneNumber, createdAt, updatedAt, id)", "type": "text/plain"}, "key": "sortBy", "value": "createdAt"}, {"description": {"content": "Sort order", "type": "text/plain"}, "key": "sortOrder", "value": "value"}], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "dd32534a-c9f7-4657-8507-389e6919a3d3", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "84b96cc0-890c-4f6d-8e5c-b0c1bec87e4a", "name": "GET /v1/users/students", "request": {"description": {"content": "Get students with filtering", "type": "text/plain"}, "url": {"path": ["v1", "users", "students"], "host": ["{{baseUrl}}"], "query": [{"description": {"content": "Search students by email or phone number", "type": "text/plain"}, "key": "search", "value": "john"}, {"description": {"content": "Page number (1-based)", "type": "text/plain"}, "key": "page", "value": "1"}, {"description": {"content": "Items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "limit", "value": "10"}, {"description": {"content": "Alias for limit - items per page (default: 10, max: 100)", "type": "text/plain"}, "key": "size", "value": "10"}, {"description": {"content": "Sort by field (email, phoneNumber, createdAt, updatedAt, id)", "type": "text/plain"}, "key": "sortBy", "value": "createdAt"}, {"description": {"content": "Sort order", "type": "text/plain"}, "key": "sortOrder", "value": "value"}], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "4f2f33d4-e842-45b1-9ff6-01010625e351", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "323597e1-7b38-4c6b-8fc3-318d495e7569", "name": "GET /v1/users/profile", "request": {"description": {"content": "Get profile", "type": "text/plain"}, "url": {"path": ["v1", "users", "profile"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "efd3e4a8-1cc4-40fa-8a90-bb1fe4b720a2", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "15236977-64ad-4cfa-9a5a-137acf0a5355", "name": "POST /v1/users/profile", "request": {"description": {"content": "Store profile", "type": "text/plain"}, "url": {"path": ["v1", "users", "profile"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"profilePicture\": \"https://courseted.com/images/profile.jpg\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "623c7b4b-daaa-4be3-a51b-967c2b7497db", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}], "event": []}, {"id": "e3c12cec-7514-4c4e-aa4e-e7e19ed16f9b", "name": "<PERSON><PERSON>", "item": [{"id": "e1ba5ffd-9251-4590-a4aa-fbd083446241", "name": "POST /v1/auth/login", "request": {"description": {"content": "<PERSON><PERSON>", "type": "text/plain"}, "url": {"path": ["v1", "auth", "login"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "ff2051e5-deed-4c9f-9db6-b3d238c4e7d2", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "512db8df-aa0b-4afa-8df1-68a861524fc6", "name": "POST /v1/auth/register", "request": {"description": {"content": "Register", "type": "text/plain"}, "url": {"path": ["v1", "auth", "register"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"phoneNumber\": \"123456789\",\n  \"countryId\": \"1\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "4d88dd7e-1b39-41af-8295-0e330b7fdca1", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "e248c686-7f87-4714-9a84-66c2b197415c", "name": "GET /v1/auth/profile", "request": {"description": {"content": "Profile", "type": "text/plain"}, "url": {"path": ["v1", "auth", "profile"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "53dc6c3c-e96e-48d9-913c-cfeebafdd8c5", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "7c6f2738-ea84-4021-a497-90a102788ebe", "name": "GET /v1/auth/google", "request": {"description": {"content": "Google Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "google"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "9bd34bea-670b-4011-a6d4-a05a87b1233c", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "6e04f272-9ec2-4a05-b081-cabb21b79182", "name": "GET /v1/auth/google/callback", "request": {"description": {"content": "Google Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "google", "callback"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "a67b9c2c-3375-4046-a70d-67f1595b0969", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "9bd60c3a-7a1a-4af1-a389-26cdca4051a9", "name": "GET /v1/auth/facebook", "request": {"description": {"content": "Facebook Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "facebook"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "3aaa70da-2233-48d4-96f8-936b372d135d", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "483ccabe-12ae-46ae-be4d-4a9b0f2af8df", "name": "GET /v1/auth/facebook/callback", "request": {"description": {"content": "Facebook Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "facebook", "callback"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "94c931f2-7eee-43b5-bc47-4d0130f86674", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "894c9cb2-5df3-46b0-a493-c52110a98af0", "name": "GET /v1/auth/linkedin", "request": {"description": {"content": "Facebook Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "linkedin"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "8cedccaf-c0a1-4eb7-a192-2f027760c47f", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "17d096d4-16b1-43de-b5cf-ac1171c84984", "name": "GET /v1/auth/linkedin/callback", "request": {"description": {"content": "Facebook Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "linkedin", "callback"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "626c4d7c-5f6b-4d50-8d0a-320d800f2c37", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "3b165a1a-d071-4359-9be9-c1a01c3886aa", "name": "POST /v1/auth/verify-otp", "request": {"description": {"content": "Facebook Callback", "type": "text/plain"}, "url": {"path": ["v1", "auth", "verify-otp"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"code\": \"123567\",\n  \"phoneNumber\": \"123456789\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "10cc9621-83c9-446b-9848-f794b9d0c4b6", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "02c87c0d-83a7-4aab-91f2-d30cc266a39c", "name": "GET /v1/auth/context", "request": {"description": {"content": "Get User Context", "type": "text/plain"}, "url": {"path": ["v1", "auth", "context"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "a8623554-279d-4482-8964-a0d3f1b9975f", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}], "event": []}, {"id": "812bf11b-a616-45b1-82a4-4c7138e9a0a5", "name": "Countries", "item": [{"id": "046cc0cc-c4c5-428f-970d-10c0457beaa6", "name": "GET /v1/countries", "request": {"description": {"content": "Get all countries", "type": "text/plain"}, "url": {"path": ["v1", "countries"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "7103de14-3492-41db-9c44-dbb4ae8a016d", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "ce824342-999b-4866-8630-0e15bc68bdc8", "name": "GET /v1/countries/{code}", "request": {"description": {"content": "Get country by code", "type": "text/plain"}, "url": {"path": ["v1", "countries", "US"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "GET"}, "response": [], "event": [{"listen": "test", "script": {"id": "4356d9d2-3fbe-43c4-802a-32f9aa3a5cf8", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}], "event": []}, {"id": "9e157b4a-d8fa-45ab-bff5-3368b4830ebd", "name": "Sms", "item": [{"id": "377436c0-860e-4886-a1c5-1d74e65c225c", "name": "POST /v1/sms/send", "request": {"description": {}, "url": {"path": ["v1", "sms", "send"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST", "body": {"mode": "raw", "raw": "{\n  \"password\": \"<EMAIL>\",\n  \"to\": \"<EMAIL>\",\n  \"body\": \"<EMAIL>\",\n  \"from\": \"<EMAIL>\",\n  \"senderId\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [], "event": [{"listen": "test", "script": {"id": "88fc9642-60fc-4454-bfea-00cc7b36d6e6", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}, {"id": "6a602d39-404d-4535-9644-f3b2b014db1a", "name": "POST /v1/sms/bulk", "request": {"description": {}, "url": {"path": ["v1", "sms", "bulk"], "host": ["{{baseUrl}}"], "query": [], "variable": []}, "header": [{"description": {"content": "Token for authentication", "type": "text/plain"}, "key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU"}, {"description": {"content": "Content Type", "type": "text/plain"}, "key": "Content-Type", "value": "application/json"}], "method": "POST"}, "response": [], "event": [{"listen": "test", "script": {"id": "27b24020-f3e5-4e07-868b-66e078463c72", "type": "text/javascript", "exec": ["pm.test('Status code is success', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}]}], "event": []}], "auth": {"type": "bearer", "bearer": [{"type": "string", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.yocgamWdd59966vPkpJW5rHiCbb1R3_XLknRN4mNNFU", "key": "token"}]}, "event": [], "variable": [{"type": "string", "value": "http://localhost:8000", "key": "baseUrl"}], "info": {"_postman_id": "fa246148-0264-4a8f-94e8-bb0efd0d2753", "name": "courseted", "version": {"raw": "1.0.0", "major": 1, "minor": 0, "patch": 0, "prerelease": [], "build": [], "string": "1.0.0"}, "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": {"content": "API collection generated from OpenAPI/Swagger documentation", "type": "text/plain"}}}