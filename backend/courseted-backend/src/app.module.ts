import { HttpModule } from "@nestjs/axios";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { LoggerModule } from "nestjs-pino";

import { AppService } from "./app.service";
import { loggerOptions } from "./config/logger";
import { TypeOrmConfig } from "./config/typeorm";
import { AuthModule } from "./modules/auth/auth.module";
import { CountryModule } from "./modules/country/country.module";
import { SmsModule } from "./modules/sms/sms.module";
import { UsersModule } from "./modules/users/users.module";

@Module({
  imports: [
    LoggerModule.forRoot(loggerOptions),
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmConfig,
    UsersModule,
    AuthModule,
    SmsModule,
    CountryModule,
    HttpModule,
  ],
  controllers: [],
  providers: [AppService, Logger],
})
export class AppModule {}
