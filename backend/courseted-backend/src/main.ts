import { Logger as NestLogger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { SwaggerModule } from "@nestjs/swagger";
import { writeFile } from "fs/promises";
import { join } from "path";

import { getApplicationInstance } from "./app";
import { getDocument } from "./document";

async function bootstrap() {
  const app = await getApplicationInstance();

  const configService = app.get(ConfigService);
  const env = configService.getOrThrow<string>("NODE_ENV");
  const PORT: number = Number(configService.getOrThrow<number>("PORT")) || 8000;

  if (env === "development") {
    const document = getDocument(app);
    SwaggerModule.setup("api-docs", app, document);
    await writeFile(
      join(__dirname, "..", "./docs/api/swagger.json"),
      JSON.stringify(document, null, 2),
    );

    NestLogger.log("API code generated successfully", "Bootstrap");
  }
  await app.listen(PORT, () => NestLogger.log(`Server Port at ${PORT}`));

  return app.getUrl();
}

(async (): Promise<void> => {
  try {
    const url = await bootstrap();
    NestLogger.log(url, "Bootstrap");
  } catch (error) {
    NestLogger.error(error, "Bootstrap");
  }
})();
