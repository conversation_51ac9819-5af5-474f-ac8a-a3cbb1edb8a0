import { ApiProperty } from "@nestjs/swagger";
import { Exclude } from "class-transformer";
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
} from "typeorm";

import { BaseEntity } from "@/common/base/base.entity";
import { UserRole } from "@/types";

import { Country } from "./country.entity";
import { Profile } from "./profile.entity";

@Entity("users")
export class User extends BaseEntity {
  @ApiProperty({
    description: "The unique identifier of the user",
    example: "1",
  })
  @PrimaryGeneratedColumn("increment")
  id?: number;

  @ApiProperty({
    description: "The email address of the user",
    example: "<EMAIL>",
  })
  @Index("idx_user_email", { unique: true })
  @Column({ unique: true })
  email: string;

  @ApiProperty({
    description: "The hashed password of the user",
    example: "hashedPassword123",
    required: false,
  })
  @Exclude()
  @Column({ nullable: true })
  password: string | null;

  @ApiProperty({
    description: "Whether the user has verified their email",
    example: false,
    default: false,
  })
  @Index("idx_user_email_verified")
  @Column({ default: false })
  isEmailVerified: boolean;

  @ApiProperty({
    description: "The role of the user in the system",
    enum: UserRole,
    enumName: "UserRole",
    example: UserRole.STUDENT,
    default: UserRole.STUDENT,
  })
  @Index("idx_user_role")
  @Column({
    type: "enum",
    enum: UserRole,
    default: UserRole.STUDENT,
  })
  role: UserRole;

  @ApiProperty({
    description: "Google OAuth ID for the user",
    example: "123456789",
    required: false,
  })
  @Index("idx_user_google_id")
  @Column({ nullable: true })
  googleId?: string;

  @ApiProperty({
    description: "Facebook OAuth ID for the user",
    example: "123456789",
    required: false,
  })
  @Index("idx_user_facebook_id")
  @Column({ nullable: true })
  facebookId?: string;

  @ApiProperty({
    description: "LinkedIn OAuth ID for the user",
    example: "123456789",
    required: false,
  })
  @Index("idx_user_linkedin_id")
  @Column({ nullable: true })
  linkedinId?: string;

  @ApiProperty({
    description: "The user's profile information",
    type: () => Profile,
  })
  @OneToOne(() => Profile, (profile) => profile.user, {
    cascade: true,
    eager: true,
  })
  profile?: Profile;

  @ApiProperty({
    description: "The country this user belongs to",
    type: () => Country,
    required: true,
  })
  @ManyToOne(() => Country, { nullable: false })
  @JoinColumn({ name: "countryId" })
  @Index("idx_user_country_id")
  country?: Country;

  @ApiProperty({
    description: "User's phone number",
    example: "+8801XXXXXXXXX",
    required: true,
  })
  @Index("idx_user_phone", { unique: true })
  @Column({ unique: true, nullable: false })
  phoneNumber?: string;

  @ApiProperty({
    description: "Whether phone is verified",
    default: false,
  })
  @Column({ default: false })
  isPhoneVerified: boolean;

  @Column({ nullable: true })
  otpCode: string;

  @Column({ nullable: true })
  otpExpiresAt: Date;
}
