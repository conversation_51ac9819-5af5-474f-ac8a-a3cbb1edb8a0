require("dotenv").config();

import { DataSource } from "typeorm";
import { PostgresConnectionOptions } from "typeorm/driver/postgres/PostgresConnectionOptions";

import { ENTITIES } from "../entities";

const postgresConfig: PostgresConnectionOptions = {
  type: "postgres",
  host: process.env.POSTGRES_HOST,
  port: Number(process.env.POSTGRES_PORT),
  username: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  database: process.env.POSTGRES_DB,
  synchronize: false,
  migrationsRun: false,
  logging: ["error"],
  entities: ENTITIES,
  migrationsTableName: "migrations",
  migrations: ["dist/database/migrations/*.js"],
  maxQueryExecutionTime: 10000,
  ssl:
    process.env.NODE_ENV === "production"
      ? { rejectUnauthorized: false }
      : false,
};

const PostgresDataSource = new DataSource(postgresConfig);

export { postgresConfig, PostgresDataSource };
