import { LoginDto } from "@modules/auth/dtos/login.dto";
import { ExecutionContext, Injectable } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { plainToClass } from "class-transformer";
import { validate } from "class-validator";

@Injectable()
export class LocalAuthGuard extends AuthGuard("local") {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const loginDto = plainToClass(LoginDto, request.body);
    const errors = await validate(loginDto);
    if (errors.length > 0) {
      for (const error of errors) {
        for (const constraint in error.constraints) {
          if (constraint) {
            const message = error.constraints[constraint];
            throw new Error(message);
          }
        }
      }
    }
    return super.canActivate(context) as Promise<boolean>;
  }
}
