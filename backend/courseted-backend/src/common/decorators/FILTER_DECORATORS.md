# Filter Decorators Usage Guide

This guide shows how to use the new filter decorators that were created to simplify Swagger documentation for filtering endpoints.

## Available Decorators

### 1. `@ApiFilter(options)`
Generic filter decorator with customizable options.

```typescript
@ApiFilter({
  searchDescription: "Custom search description",
  sortableFields: ["name", "email", "createdAt"],
  maxLimit: 50,
  defaultLimit: 20,
})
```

### 2. `@ApiUserFilter()`
Predefined filter for user endpoints with search on email/phone and sorting by user fields.

### 3. `@ApiInstructorFilter()`
Predefined filter for instructor endpoints.

### 4. `@ApiStudentFilter()`
Predefined filter for student endpoints.

### 5. `@ApiPagination(options)`
Simple pagination decorator without search functionality.

## Usage Examples

### Before (Manual ApiQuery decorators):
```typescript
@ApiQuery({
  name: "search",
  required: false,
  description: "Search users by email or phone number",
})
@ApiQuery({
  name: "page",
  required: false,
  description: "Page number (default: 1)",
})
@ApiQuery({
  name: "limit",
  required: false,
  description: "Items per page (default: 10, max: 100)",
})
@ApiQuery({
  name: "sortBy",
  required: false,
  description: "Sort by field (email, phoneNumber, createdAt, role, etc.)",
})
@ApiQuery({
  name: "sortOrder",
  required: false,
  description: "Sort order (asc or desc)",
})
@Get()
async findAll(@Query() filters: BaseFilterDto) {
  return this.service.findAllWithFilters(filters);
}
```

### After (Using Filter Decorators):
```typescript
@ApiUserFilter()
@Get()
async findAll(@Query() filters: BaseFilterDto) {
  return this.service.findAllWithFilters(filters);
}
```

## Custom Filter Examples

### Products Endpoint
```typescript
@ApiFilter({
  searchDescription: "Search products by name, description, or SKU",
  sortableFields: ["name", "price", "createdAt", "category"],
  maxLimit: 200,
  defaultLimit: 25,
})
@Get("products")
async findProducts(@Query() filters: BaseFilterDto) {
  return this.productsService.findAllWithFilters(filters);
}
```

### Simple Pagination Only
```typescript
@ApiPagination({ maxLimit: 50, defaultLimit: 15 })
@Get("categories")
async findCategories(@Query() pagination: PaginationDto) {
  return this.categoriesService.findAll(pagination);
}
```

## Benefits

1. **DRY Principle**: Reduces code duplication
2. **Consistency**: Ensures all filter endpoints have the same parameter structure
3. **Maintainability**: Easy to update filter documentation in one place
4. **Type Safety**: Built-in TypeScript support
5. **Customizable**: Flexible options for different use cases

## Generated Swagger Documentation

The decorators automatically generate complete Swagger documentation including:
- Parameter descriptions
- Example values
- Data types
- Validation constraints (min/max values)
- Enum values for sortOrder

This ensures your API documentation is always complete and consistent across all filtered endpoints.
