import { SmsQueued, SmsStatus } from "@entities/sms.entity";
import { InjectQueue } from "@nestjs/bullmq";
import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectRepository } from "@nestjs/typeorm";
import { Queue } from "bullmq";
import { Repository } from "typeorm";

import { SMS_QUEUE } from "@/utils/constants";

import { Sms } from "../interfaces/sms";

@Injectable()
export class SmsService {
  constructor(
    @InjectQueue(SMS_QUEUE) private smsQueue: Queue,
    private configService: ConfigService,
    @InjectRepository(SmsQueued)
    private smsRepository: Repository<SmsQueued>,
  ) {}

  /**
   * Add an SMS message to the queue and store in database
   * @param messageDto The SMS message to send
   * @returns The job ID
   */
  async queueSms(messageDto: Sms): Promise<string> {
    // Create a new SMS message entity
    const smsMessage = new SmsQueued();
    smsMessage.to = messageDto.to;
    smsMessage.body = messageDto.body;
    smsMessage.from =
      messageDto.from || this.configService.get("TWILIO_PHONE_NUMBER");
    smsMessage.status = SmsStatus.QUEUED;

    if (messageDto.senderId) {
      smsMessage.senderId = messageDto.senderId;
    }

    // Save to database first
    await this.smsRepository.save(smsMessage);

    // Add to queue
    const job = await this.smsQueue.add(
      SMS_QUEUE,
      {
        to: smsMessage.to,
        body: smsMessage.body,
        from: smsMessage.from,
        id: smsMessage.id, // Pass the database ID to link the job with the database record
      },
      {
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 5000,
        },
        removeOnComplete: true,
        removeOnFail: false,
      },
    );

    // Update the job ID in the database
    smsMessage.jobId = job.id;
    await this.smsRepository.save(smsMessage);

    return job.id;
  }

  /**
   * Queue multiple SMS messages at once
   * @param messagesDto Array of SMS messages to send
   * @returns Array of job IDs
   */
  async queueBulkSms(messagesDto: Sms[]): Promise<string[]> {
    const jobIds: string[] = [];

    for (const messageDto of messagesDto) {
      const jobId = await this.queueSms(messageDto);
      jobIds.push(jobId);
    }

    return jobIds;
  }

  /**
   * Update SMS status in the database
   * @param id The SMS message ID
   * @param status The new status
   * @param twilioSid Optional Twilio SID
   * @param errorMessage Optional error message
   */
  async updateSmsStatus(
    id: number,
    status: SmsStatus,
    twilioSid?: string,
    errorMessage?: string,
  ): Promise<SmsQueued> {
    const smsMessage = await this.smsRepository.findOne({ where: { id } });

    if (!smsMessage) {
      throw new Error(`SMS message with ID ${id} not found`);
    }

    smsMessage.status = status;

    if (twilioSid) {
      smsMessage.twilioSid = twilioSid;
    }

    if (errorMessage) {
      smsMessage.errorMessage = errorMessage;
    }

    return this.smsRepository.save(smsMessage);
  }

  /**
   * Get SMS message by ID
   * @param id The SMS message ID
   */
  async getSmsById(id: number): Promise<SmsQueued> {
    return this.smsRepository.findOne({ where: { id } });
  }

  /**
   * Get all SMS messages with optional filtering
   * @param status Optional status filter
   * @param senderId Optional sender ID filter
   */
  async getAllSms(status?: SmsStatus, senderId?: number): Promise<SmsQueued[]> {
    const query: any = {};

    if (status) {
      query.status = status;
    }

    if (senderId) {
      query.senderId = senderId;
    }

    return this.smsRepository.find({
      where: query,
      order: { createdAt: "DESC" },
    });
  }
}
