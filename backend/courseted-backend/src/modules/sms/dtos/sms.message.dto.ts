import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsNumber, IsString } from "class-validator";

import { Sms } from "../interfaces/sms";

export class SmsDto implements Sms {
  @ApiProperty({
    example: "<EMAIL>",
    description: "The email of the user",
  })
  @IsString()
  @IsNotEmpty()
  password: string;
  @ApiProperty({
    example: "<EMAIL>",
    description: "The email of the user",
  })
  @IsString()
  @IsNotEmpty()
  to: string;

  @ApiProperty({
    example: "<EMAIL>",
    description: "The email of the user",
  })
  @IsString()
  @IsNotEmpty()
  body: string;

  @ApiProperty({
    example: "<EMAIL>",
    description: "The email of the user",
  })
  @IsString()
  @IsNotEmpty()
  from?: string;

  @ApiProperty({
    example: "<EMAIL>",
    description: "The email of the user",
  })
  @IsNumber()
  @IsNotEmpty()
  senderId?: number;
}
