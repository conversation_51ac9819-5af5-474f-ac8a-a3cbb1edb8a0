import { SmsQueued } from "@entities/sms.entity";
import { BullModule } from "@nestjs/bullmq";
import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import { TwilioModule } from "nestjs-twilio";

import { SMS_QUEUE } from "@/utils/constants";

import { SmsController } from "./controllers/sms.controller";
import { SmsService } from "./services/sms.service";
import { SmsProcessor } from "./sms.processor";

@Module({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forFeature([SmsQueued]),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        connection: {
          host: configService.getOrThrow("REDIS_HOST", "localhost"),
          port: configService.getOrThrow("REDIS_PORT", 6379),
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: SMS_QUEUE,
    }),
    TwilioModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        accountSid: configService.get("TWILIO_ACCOUNT_SID"),
        authToken: configService.get("TWILIO_AUTH_TOKEN"),
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [SmsController],
  providers: [SmsService, SmsProcessor],
  exports: [SmsService],
})
export class SmsModule {}
