import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Post,
  UseGuards,
  UseInterceptors,
} from "@nestjs/common";

import { JwtAuthGuard } from "@/common/guards/jwt-auth.guard";
import { ResponseInterceptor } from "@/common/interceptors/response.interceptor";

import { SmsDto } from "../dtos/sms.message.dto";
import { SmsService } from "../services/sms.service";

@Controller({
  path: "sms",
  version: "1",
})
@UseGuards(JwtAuthGuard)
@UseInterceptors(ClassSerializerInterceptor, ResponseInterceptor)
export class SmsController {
  constructor(private readonly smsService: SmsService) {}

  @Post("send")
  async sendSms(@Body() message: SmsDto) {
    const jobId = await this.smsService.queueSms(message);
    return { success: true, jobId };
  }

  @Post("bulk")
  async sendBulkSms(@Body() payload: { messages: SmsDto[] }) {
    const jobIds = await this.smsService.queueBulkSms(payload.messages);
    return { success: true, count: jobIds.length, jobIds };
  }
}
