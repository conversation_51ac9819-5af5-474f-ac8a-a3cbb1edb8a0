import {
  ClassSerializerInterceptor,
  Controller,
  Get,
  Param,
  UseInterceptors,
} from "@nestjs/common";
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from "@nestjs/swagger";

import { ResponseInterceptor } from "@/common/interceptors/response.interceptor";
import { Country } from "@/entities/country.entity";

import { CountryService } from "../services/country.service";

@ApiTags("Countries")
@Controller({
  path: "countries",
  version: "1",
})
@UseInterceptors(ClassSerializerInterceptor, ResponseInterceptor)
export class CountryController {
  constructor(private readonly countryService: CountryService) {}

  @ApiOperation({
    summary: "Get all countries",
    description:
      "Retrieve all countries with their codes, names, phone codes, and flags",
  })
  @ApiResponse({
    status: 200,
    description: "List of all countries",
    type: [Country],
  })
  @Get()
  async findAll(): Promise<Country[]> {
    return this.countryService.findAll();
  }

  @ApiOperation({
    summary: "Get country by code",
    description: "Retrieve a specific country by its ISO 2-letter code",
  })
  @ApiParam({
    name: "code",
    description: "ISO 2-letter country code (e.g., US, UK, CA)",
    example: "US",
  })
  @ApiResponse({
    status: 200,
    description: "Country found",
    type: Country,
  })
  @ApiResponse({
    status: 404,
    description: "Country not found",
  })
  @Get(":code")
  async findByCode(@Param("code") code: string): Promise<Country | null> {
    return this.countryService.findByCode(code.toUpperCase());
  }
}
