import { ApiProperty } from "@nestjs/swagger";
import {
  <PERSON><PERSON>mail,
  IsNotEmpty,
  IsNumber,
  IsString,
  MinLength,
} from "class-validator";

export class RegisterDto {
  @ApiProperty({
    description: "The email address of the user",
    example: "<EMAIL>",
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: "The password of the user",
    example: "password123",
    minLength: 6,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: "The phone number of the user",
    example: "123456789",
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @ApiProperty({
    description: "The country ID of the user",
    example: "1",
    required: true,
  })
  @IsNumber()
  @IsNotEmpty()
  countryId: number;
}
