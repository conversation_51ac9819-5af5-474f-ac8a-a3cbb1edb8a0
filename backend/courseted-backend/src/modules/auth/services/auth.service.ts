import { Profile, User } from "@entities";
import { CountryService } from "@modules/country/services/country.service";
import { SmsService } from "@modules/sms/services/sms.service";
import { UsersService } from "@modules/users/services/users.service";
import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";

import { comparePassword, hashPassword } from "@/utils";

import { RegisterDto } from "../dtos/register.dto";
import { LoginResponse, RegisterResponse } from "../types/auth-response.type";
import { UserContextResponse } from "../types/context-response.type";
import { JwtPayload } from "../types/jwt-payload.type";
import {
  OAuthProfile,
  OAuthProvider,
  OAuthProviderType,
} from "../types/oauth-profile.type";

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly smsService: SmsService,
    private readonly countryService: CountryService,
  ) {}

  async validateUser(
    email: string,
    password: string,
  ): Promise<Omit<User, "password"> | null> {
    const user = await this.usersService.findOne(email);
    console.log("user", user);
    if (!user || !user.password) return null;
    const isPasswordValid = await this.verifyPassword(password, user.password);
    if (!isPasswordValid) return null;

    const { password: _, ...result } = user;
    return result;
  }

  async login(user: Omit<User, "password">): Promise<LoginResponse> {
    const payload: JwtPayload = {
      email: user.email,
      sub: String(user.id),
      role: user.role,
    };

    return {
      accessToken: await this.jwtService.signAsync(payload),
      user: this.sanitizeUser(user),
    };
  }

  async registerUser(registerDto: RegisterDto): Promise<RegisterResponse> {
    console.log("registerDto", registerDto);

    // Check if user already exists
    const existingUser = await this.usersService.findByEmail(registerDto.email);
    if (existingUser) {
      throw new ConflictException("User with this email already exists");
    }

    // Check if phone number already exists
    const existingPhoneUser = await this.usersService.findByPhone(
      registerDto.phoneNumber,
    );
    if (existingPhoneUser) {
      throw new ConflictException("User with this phone number already exists");
    }

    // Validate country exists
    const country = await this.countryService.findById(registerDto.countryId);
    if (!country) {
      throw new BadRequestException("Invalid country ID");
    }

    // Generate OTP
    const otpCode = this.generateOtpCode();
    const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now

    // Create user with all required properties
    const user = new User();
    user.email = registerDto.email;
    user.password = await hashPassword(registerDto.password);
    user.phoneNumber = registerDto.phoneNumber;
    user.country = country;
    user.otpCode = otpCode;
    user.otpExpiresAt = otpExpiresAt;
    user.isEmailVerified = false;
    user.isPhoneVerified = false;

    const savedUser = await this.usersService.create(user);

    // Send OTP via SMS
    try {
      await this.smsService.queueSms({
        to: registerDto.phoneNumber,
        body: `Your OTP code is: ${otpCode}. This code will expire in 10 minutes.`,
        senderId: savedUser.id,
      });
    } catch (error) {
      console.error("Failed to send OTP SMS:", error);
      // Don't fail registration if SMS fails, but log the error
    }

    // Generate JWT token
    const token = this.jwtService.sign({
      sub: savedUser.id,
      email: savedUser.email,
    });

    return {
      user: savedUser,
      token,
      message:
        "Registration successful. Please verify your phone number with the OTP sent via SMS.",
    };
  }

  async validateOAuthLogin(
    profile: OAuthProfile,
    provider: OAuthProviderType,
  ): Promise<LoginResponse> {
    const user = await this.findOrCreateOAuthUser(profile, provider);
    return this.login(user);
  }

  private async verifyPassword(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    if (!plainPassword || !hashedPassword) return false;
    return await comparePassword(plainPassword, hashedPassword);
  }

  private sanitizeUser(user: Omit<User, "password">): Omit<any, "password"> {
    return {
      id: user.id,
      email: user.email,
      isEmailVerified: false,
      role: user.role,
      profile: user.profile,
      isPhoneVerified: user.isPhoneVerified,
    };
  }

  private async validateNewUser(email: string): Promise<void> {
    const existingUser = await this.usersService.findOne(email);
    if (existingUser) {
      throw new UnauthorizedException("User with this email already exists");
    }
  }

  private async findOrCreateOAuthUser(
    profile: OAuthProfile,
    provider: OAuthProviderType,
  ): Promise<User> {
    if (
      ![
        OAuthProvider.GOOGLE,
        OAuthProvider.FACEBOOK,
        OAuthProvider.LINKEDIN,
      ].includes(provider)
    ) {
      throw new UnauthorizedException(`Unsupported provider: ${provider}`);
    }

    if (provider === OAuthProvider.GOOGLE) {
      return this.handleGoogleUser(profile);
    } else if (provider === OAuthProvider.FACEBOOK) {
      return this.handleFacebookUser(profile);
    } else if (provider === OAuthProvider.LINKEDIN) {
      return this.handleLinkedInUser(profile);
    }
  }

  private async handleGoogleUser(profile: OAuthProfile): Promise<User> {
    let user = await this.usersService.findByGoogleId(profile.id);
    if (user) return user;

    user = await this.usersService.findOne(profile.emails[0].value);

    if (user) {
      return this.usersService.update(user.id, { googleId: profile.id });
    }

    // Get default country (US) for OAuth users
    const defaultCountry = await this.countryService.findByCode("US");
    if (!defaultCountry) {
      throw new BadRequestException(
        "Default country not found. Please seed countries first.",
      );
    }

    const newUser = new User();
    newUser.email = profile.emails[0].value;
    newUser.googleId = profile.id;
    newUser.isEmailVerified = true;
    newUser.country = defaultCountry;

    // Create profile for the user
    const userProfile = new Profile();
    userProfile.firstName = profile.name?.givenName;
    userProfile.lastName = profile.name?.familyName;
    userProfile.profilePicture = profile.photos?.[0]?.value;
    newUser.profile = userProfile;

    return this.usersService.create(newUser);
  }

  private async handleFacebookUser(profile: OAuthProfile): Promise<User> {
    let user = await this.usersService.findByFacebookId(profile.id);
    if (user) return user;

    user = await this.usersService.findOne(profile.emails[0].value);

    if (user) {
      return this.usersService.update(user.id, { facebookId: profile.id });
    }

    // Get default country (US) for OAuth users
    const defaultCountry = await this.countryService.findByCode("US");
    if (!defaultCountry) {
      throw new BadRequestException(
        "Default country not found. Please seed countries first.",
      );
    }

    const newUser = new User();
    newUser.email = profile.emails[0].value;
    newUser.facebookId = profile.id;
    newUser.isEmailVerified = true;
    newUser.country = defaultCountry;

    // Create profile for the user
    const userProfile = new Profile();
    userProfile.firstName = profile.name?.givenName;
    userProfile.lastName = profile.name?.familyName;
    userProfile.profilePicture = profile.photos?.[0]?.value;
    newUser.profile = userProfile;

    return this.usersService.create(newUser);
  }

  private async handleLinkedInUser(profile: OAuthProfile): Promise<User> {
    let user = await this.usersService.findByLinkedInId(profile.id);
    if (user) return user;

    user = await this.usersService.findOne(profile.emails[0].value);

    if (user) {
      return this.usersService.update(user.id, { linkedinId: profile.id });
    }

    // Get default country (US) for OAuth users
    const defaultCountry = await this.countryService.findByCode("US");
    if (!defaultCountry) {
      throw new BadRequestException(
        "Default country not found. Please seed countries first.",
      );
    }

    const newUser = new User();
    newUser.email = profile.emails[0].value;
    newUser.linkedinId = profile.id;
    newUser.isEmailVerified = true;
    newUser.country = defaultCountry;

    // Create profile for the user
    const userProfile = new Profile();
    userProfile.firstName = profile.name?.givenName;
    userProfile.lastName = profile.name?.familyName;
    userProfile.profilePicture = profile.photos?.[0]?.value;
    newUser.profile = userProfile;

    return this.usersService.create(newUser);
  }

  async verifyOtp(phoneNumber: string, code: string) {
    const user = await this.usersService.findByPhone(phoneNumber);
    if (!user) throw new NotFoundException("User not found");

    if (user.otpCode !== code || new Date() > user.otpExpiresAt) {
      throw new UnauthorizedException("Invalid or expired OTP");
    }

    user.isPhoneVerified = true;
    user.otpCode = null;
    user.otpExpiresAt = null;

    await this.usersService.update(user.id, user);
    return { success: true };
  }

  async getUserContext(
    user: Omit<User, "password">,
  ): Promise<UserContextResponse> {
    const fullUser = await this.usersService.findByIdWithProfile(user.id);

    if (!fullUser) {
      throw new NotFoundException("User not found");
    }

    return {
      user: this.sanitizeUser(fullUser),
      permissions: this.getUserPermissions(fullUser.role),
      preferences: {
        theme: "light",
        language: "en",
      },
      meta: {
        lastLogin: fullUser.updatedAt,
        isEmailVerified: fullUser.isEmailVerified,
        isPhoneVerified: fullUser.isPhoneVerified,
      },
    };
  }

  private getUserPermissions(role: string): string[] {
    // Define permissions based on user role
    const permissions: Record<string, string[]> = {
      admin: ["read", "write", "delete", "manage_users", "manage_courses"],
      instructor: ["read", "write", "manage_courses"],
      student: ["read"],
    };

    return permissions[role] || permissions.student;
  }

  /**
   * Generate a 6-digit OTP code
   */
  private generateOtpCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Send OTP to user's phone number
   */
  async sendOtp(
    phoneNumber: string,
  ): Promise<{ success: boolean; message: string }> {
    const user = await this.usersService.findByPhone(phoneNumber);
    if (!user) {
      throw new NotFoundException("User not found with this phone number");
    }

    if (user.isPhoneVerified) {
      throw new BadRequestException("Phone number is already verified");
    }

    // Generate new OTP
    const otpCode = this.generateOtpCode();
    const otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Update user with new OTP
    await this.usersService.update(user.id, {
      otpCode,
      otpExpiresAt,
    });

    // Send OTP via SMS
    try {
      await this.smsService.queueSms({
        to: phoneNumber,
        body: `Your OTP code is: ${otpCode}. This code will expire in 10 minutes.`,
        senderId: user.id,
      });

      return {
        success: true,
        message: "OTP sent successfully",
      };
    } catch (error) {
      console.error("Failed to send OTP SMS:", error);
      throw new BadRequestException("Failed to send OTP. Please try again.");
    }
  }

  /**
   * Resend OTP to user's phone number
   */
  async resendOtp(
    phoneNumber: string,
  ): Promise<{ success: boolean; message: string }> {
    const user = await this.usersService.findByPhone(phoneNumber);
    if (!user) {
      throw new NotFoundException("User not found with this phone number");
    }

    if (user.isPhoneVerified) {
      throw new BadRequestException("Phone number is already verified");
    }

    // Check if user can request a new OTP (rate limiting)
    if (user.otpExpiresAt && user.otpExpiresAt > new Date()) {
      const remainingTime = Math.ceil(
        (user.otpExpiresAt.getTime() - Date.now()) / 1000 / 60,
      );
      throw new BadRequestException(
        `Please wait ${remainingTime} minutes before requesting a new OTP`,
      );
    }

    return this.sendOtp(phoneNumber);
  }
}
