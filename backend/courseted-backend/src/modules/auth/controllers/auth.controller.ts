import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  Post,
  Req,
  Res,
  UseFilters,
  UseGuards,
  UseInterceptors,
} from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { Response } from "express";

import { Swagger } from "@/common/decorators";
import { CommonExceptionFilter } from "@/common/filter/exception.filter";
import { JwtAuthGuard } from "@/common/guards/jwt-auth.guard";
import { LocalAuthGuard } from "@/common/guards/local-auth.guard";
import { ResponseInterceptor } from "@/common/interceptors/response.interceptor";

import { LoginDto } from "../dtos/login.dto";
import { RegisterDto } from "../dtos/register.dto";
import { VerifyOtpDto } from "../dtos/verify-otp.dto";
import { AuthService } from "../services/auth.service";
import { LoginResponse, RegisterResponse } from "../types/auth-response.type";
import { UserContextResponse } from "../types/context-response.type";
import { OAuthRequest, RequestWithUser } from "../types/request.type";

@Controller({
  path: "auth",
  version: "1",
})
@UseFilters(CommonExceptionFilter)
@UseInterceptors(ClassSerializerInterceptor, ResponseInterceptor)
export class AuthController {
  constructor(private authService: AuthService) {}

  @Swagger({
    summary: "Login",
    operationId: "login",
    description: "Login with email and password",
    authentication: false,
    tags: ["Auth"],
  })
  @UseGuards(LocalAuthGuard)
  @Post("login")
  async login(
    @Body() loginDto: LoginDto,
    @Req() req: RequestWithUser,
  ): Promise<LoginResponse> {
    return this.authService.login(req.user);
  }

  @Swagger({
    summary: "Register",
    operationId: "register",
    description: "Register",
    authentication: false,
    tags: ["Auth"],
  })
  @Post("register")
  async register(@Body() registerDto: RegisterDto): Promise<RegisterResponse> {
    const user = await this.authService.registerUser(registerDto);
    return user;
  }

  @Swagger({
    summary: "Profile",
    operationId: "profile",
    description: "profile",
    authentication: true,
    tags: ["Auth"],
  })
  @UseGuards(JwtAuthGuard)
  @Get("profile")
  getProfile(@Req() req: RequestWithUser) {
    return req.user;
  }

  @Swagger({
    summary: "Google Callback",
    operationId: "google",
    description: "Google login",
    authentication: false,
    tags: ["Auth"],
  })
  @Get("google")
  @UseGuards(AuthGuard("google"))
  googleAuth() {
    // This route initiates the Google OAuth flow
  }

  @Swagger({
    summary: "Google Callback",
    operationId: "googleCallback",
    description: "Login with email and password",
    authentication: false,
    tags: ["Auth"],
  })
  @Get("google/callback")
  @UseGuards(AuthGuard("google"))
  googleAuthCallback(@Req() req: OAuthRequest, @Res() res: Response) {
    const jwt = req.user.jwt;
    // Redirect to frontend with token
    return res.redirect(
      `${process.env.FRONTEND_URL}/auth/social-login?token=${jwt}`,
    );
  }

  // Facebook Authentication
  @Swagger({
    summary: "Facebook Callback",
    operationId: "facebookAuth",
    description: "Login with email and password",
    authentication: false,
    tags: ["Auth"],
  })
  @Get("facebook")
  @UseGuards(AuthGuard("facebook"))
  facebookAuth() {
    // This route initiates the Facebook OAuth flow
  }

  // Facebook Authentication
  @Swagger({
    summary: "Facebook Callback",
    operationId: "facebookCallback",
    description: "Facebook login",
    authentication: false,
    tags: ["Auth"],
  })
  @Get("facebook/callback")
  @UseGuards(AuthGuard("facebook"))
  facebookAuthCallback(@Req() req: OAuthRequest, @Res() res: Response) {
    const jwt = req.user.jwt;
    // Redirect to frontend with token
    return res.redirect(
      `${process.env.FRONTEND_URL}/auth/social-login?token=${jwt}`,
    );
  }
  // LinkedIn Authentication
  @Swagger({
    summary: "Facebook Callback",
    operationId: "linkedinAuth",
    description: "Facebook login",
    authentication: false,
    tags: ["Auth"],
  })
  @Get("linkedin")
  @UseGuards(AuthGuard("linkedin"))
  linkedinAuth() {
    // This route initiates the LinkedIn OAuth flow
  }

  @Swagger({
    summary: "Facebook Callback",
    operationId: "linkedinAuthCallback",
    description: "linkedin login",
    authentication: false,
    tags: ["Auth"],
  })
  @Get("linkedin/callback")
  @UseGuards(AuthGuard("linkedin"))
  linkedinAuthCallback(@Req() req: OAuthRequest, @Res() res: Response) {
    const jwt = req.user.jwt;
    // Redirect to frontend with token
    return res.redirect(
      `${process.env.FRONTEND_URL}/auth/social-login?token=${jwt}`,
    );
  }

  @Swagger({
    summary: "Facebook Callback",
    operationId: "verifyOtp",
    description: "verifyOtp",
    authentication: false,
    tags: ["Auth"],
  })
  @Post("verify-otp")
  async verifyOtp(@Body() { phoneNumber, code }: VerifyOtpDto) {
    return this.authService.verifyOtp(phoneNumber, code);
  }

  @Swagger({
    summary: "Get User Context",
    operationId: "context",
    description: "Get current user context data for React client",
    authentication: true,
    tags: ["Auth"],
  })
  @UseGuards(JwtAuthGuard)
  @Get("context")
  async getContext(@Req() req: RequestWithUser): Promise<UserContextResponse> {
    return await this.authService.getUserContext(req.user);
  }
}
