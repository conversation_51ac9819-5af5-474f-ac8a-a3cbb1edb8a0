import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication, ValidationPipe } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "@/app.module";
import { getRepositoryToken } from "@nestjs/typeorm";
import { User } from "@entities/user.entity";
import { Profile } from "@entities/profile.entity";
import { Country } from "@entities/country.entity";
import { SmsQueued } from "@entities/sms.entity";
import { Repository } from "typeorm";
import { JwtService } from "@nestjs/jwt";
import * as bcrypt from "bcrypt";

describe("AuthController (e2e)", () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let profileRepository: Repository<Profile>;
  let countryRepository: Repository<Country>;
  let smsRepository: Repository<SmsQueued>;
  let jwtService: JwtService;

  const testUser = {
    email: "<EMAIL>",
    password: "Password123!",
    firstName: "Test",
    lastName: "User",
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true }));

    userRepository = moduleFixture.get<Repository<User>>(
      getRepositoryToken(User),
    );
    profileRepository = moduleFixture.get<Repository<Profile>>(
      getRepositoryToken(Profile),
    );
    countryRepository = moduleFixture.get<Repository<Country>>(
      getRepositoryToken(Country),
    );
    smsRepository = moduleFixture.get<Repository<SmsQueued>>(
      getRepositoryToken(SmsQueued),
    );
    jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.init();

    // Clean up test data in correct order (children first, then parents)
    await smsRepository.delete({});
    await profileRepository.delete({});
    await userRepository.delete({});
    await countryRepository.delete({});
  });

  afterAll(async () => {
    await smsRepository.delete({});
    await profileRepository.delete({});
    await userRepository.delete({});
    await countryRepository.delete({});
    await app.close();
  });

  afterEach(async () => {
    // Clean up after each test in correct order (children first, then parents)
    await smsRepository.delete({});
    await profileRepository.delete({});
    await userRepository.delete({});
    await countryRepository.delete({});
  });

  describe("Registration", () => {
    it("should register a new user", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send(testUser)
        .expect(201)
        .expect((res) => {
          expect(res.body.data).toHaveProperty("user");
          expect(res.body.data).toHaveProperty("accessToken");
          expect(res.body.data.user.email).toBe(testUser.email);
          expect(res.body.data.user.firstName).toBe(testUser.firstName);
          expect(res.body.data.user.lastName).toBe(testUser.lastName);
          expect(res.body.data.user).not.toHaveProperty("password");
        });
    });

    it("should not register a user with an existing email", async () => {
      const hashedPassword = await bcrypt.hash("Password123!", 10);
      await userRepository.save({
        email: "<EMAIL>",
        password: hashedPassword,
        firstName: "Existing",
        lastName: "User",
      });

      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({
          email: "<EMAIL>",
          password: "Password123!",
          firstName: "Another",
          lastName: "User",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("User with this email already exists");
        });
    });

    it("should validate registration input - invalid email", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({
          email: "invalid-email",
          password: "Password123!",
          firstName: "Test",
          lastName: "User",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("email must be an email");
        });
    });

    it("should validate registration input - weak password", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({
          email: "<EMAIL>",
          password: "123",
          firstName: "Test",
          lastName: "User",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("password");
        });
    });

    it("should validate registration input - missing required fields", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({
          email: "<EMAIL>",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("password");
        });
    });

    it("should validate registration input - empty firstName", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({
          email: "<EMAIL>",
          password: "Password123!",
          firstName: "",
          lastName: "User",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("firstName");
        });
    });

    it("should validate registration input - empty lastName", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({
          email: "<EMAIL>",
          password: "Password123!",
          firstName: "Test",
          lastName: "",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("lastName");
        });
    });
  });

  describe("Login", () => {
    beforeEach(async () => {
      const hashedPassword = await bcrypt.hash("Password123!", 10);
      await userRepository.save({
        email: "<EMAIL>",
        password: hashedPassword,
        firstName: "Login",
        lastName: "User",
      });
    });

    it("should login with valid credentials", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: "<EMAIL>",
          password: "Password123!",
        })
        .expect(201)
        .expect((res) => {
          expect(res.body.data).toHaveProperty("accessToken");
          expect(res.body.data).toHaveProperty("user");
          expect(res.body.data.user.email).toBe("<EMAIL>");
          expect(res.body.data.user).not.toHaveProperty("password");
        });
    });

    it("should not login with invalid email", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: "<EMAIL>",
          password: "Password123!",
        })
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain("Invalid credentials");
        });
    });

    it("should not login with invalid password", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: "<EMAIL>",
          password: "WrongPassword",
        })
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain("Invalid credentials");
        });
    });

    it("should validate login input - invalid email format", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: "invalid-email",
          password: "Password123!",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("email must be an email");
        });
    });

    it("should validate login input - missing password", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: "<EMAIL>",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("password");
        });
    });

    it("should validate login input - empty credentials", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({})
        .expect(400);
    });
  });

  describe("Profile", () => {
    let authToken: string;
    let testUserId: number;

    beforeEach(async () => {
      const hashedPassword = await bcrypt.hash("Password123!", 10);
      const user = await userRepository.save({
        email: "<EMAIL>",
        password: hashedPassword,
        firstName: "Profile",
        lastName: "User",
      });

      testUserId = user.id;
      authToken = jwtService.sign({
        email: user.email,
        sub: user.id,
      });
    });

    it("should get user profile with valid token", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/profile")
        .set("Authorization", `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.data.email).toBe("<EMAIL>");
          expect(res.body.data.firstName).toBe("Profile");
          expect(res.body.data.lastName).toBe("User");
          expect(res.body.data).not.toHaveProperty("password");
        });
    });

    it("should not get profile without token", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/profile")
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain("Unauthorized");
        });
    });

    it("should not get profile with invalid token", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/profile")
        .set("Authorization", "Bearer invalid-token")
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain("Unauthorized");
        });
    });

    it("should not get profile with malformed authorization header", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/profile")
        .set("Authorization", "InvalidFormat")
        .expect(401);
    });

    it("should not get profile with expired token", async () => {
      const expiredToken = jwtService.sign(
        {
          email: "<EMAIL>",
          sub: testUserId,
        },
        { expiresIn: "0s" }
      );

      // Wait a moment to ensure token is expired
      await new Promise(resolve => setTimeout(resolve, 100));

      return request(app.getHttpServer())
        .get("/v1/auth/profile")
        .set("Authorization", `Bearer ${expiredToken}`)
        .expect(401);
    });
  });

  describe("User Context", () => {
    let authToken: string;
    let user: User;
    let profile: Profile;
    let testCountry: Country;

    beforeEach(async () => {
      const hashedPassword = await bcrypt.hash("Password123!", 10);
      user = await userRepository.save({
        email: "<EMAIL>",
        password: hashedPassword,
        firstName: "Context",
        lastName: "User",
      });

      // Create a test country
      testCountry = await countryRepository.save({
        name: "Test Country",
        code: "TC",
        iso3: "TCO",
        phoneCode: "+999",
        flag: "🏳️",
      });

      profile = await profileRepository.save({
        firstName: "Context",
        lastName: "User",
        user: user,
        country: testCountry,
      });

      authToken = jwtService.sign({
        email: user.email,
        sub: user.id,
      });
    });

    it("should get user context with valid token", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/context")
        .set("Authorization", `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveProperty("user");
          expect(res.body.data).toHaveProperty("profile");
          expect(res.body.data).toHaveProperty("permissions");
          expect(res.body.data).toHaveProperty("settings");
          expect(res.body.data.user.email).toBe("<EMAIL>");
          expect(res.body.data.user.firstName).toBe("Context");
          expect(res.body.data.user.lastName).toBe("User");
          expect(res.body.data.user).not.toHaveProperty("password");
          expect(res.body.data.profile.firstName).toBe("Context");
          expect(res.body.data.profile.lastName).toBe("User");
        });
    });

    it("should get user context without profile", async () => {
      // Create user without profile
      const hashedPassword = await bcrypt.hash("Password123!", 10);
      const userWithoutProfile = await userRepository.save({
        email: "<EMAIL>",
        password: hashedPassword,
        firstName: "NoProfile",
        lastName: "User",
      });

      const tokenWithoutProfile = jwtService.sign({
        email: userWithoutProfile.email,
        sub: userWithoutProfile.id,
      });

      return request(app.getHttpServer())
        .get("/v1/auth/context")
        .set("Authorization", `Bearer ${tokenWithoutProfile}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveProperty("user");
          expect(res.body.data).toHaveProperty("profile");
          expect(res.body.data).toHaveProperty("permissions");
          expect(res.body.data).toHaveProperty("settings");
          expect(res.body.data.user.email).toBe("<EMAIL>");
          expect(res.body.data.profile).toBeNull();
        });
    });

    it("should not get context without token", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/context")
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain("Unauthorized");
        });
    });

    it("should not get context with invalid token", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/context")
        .set("Authorization", "Bearer invalid-token")
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain("Unauthorized");
        });
    });
  });

  describe("OTP Verification", () => {
    it("should validate OTP input - missing phoneNumber", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/verify-otp")
        .send({
          code: "123456",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("phoneNumber");
        });
    });

    it("should validate OTP input - missing code", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/verify-otp")
        .send({
          phoneNumber: "+1234567890",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("code");
        });
    });

    it("should validate OTP input - invalid phone number format", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/verify-otp")
        .send({
          phoneNumber: "invalid-phone",
          code: "123456",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("phoneNumber");
        });
    });

    it("should validate OTP input - invalid code format", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/verify-otp")
        .send({
          phoneNumber: "+1234567890",
          code: "abc",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("code");
        });
    });

    it("should handle invalid OTP", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/verify-otp")
        .send({
          phoneNumber: "+1234567890",
          code: "000000",
        })
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain("Invalid");
        });
    });
  });

  describe("OAuth Routes", () => {
    it("should redirect to Google OAuth", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/google")
        .expect(302)
        .expect((res) => {
          expect(res.headers.location).toContain("google");
        });
    });

    it("should redirect to Facebook OAuth", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/facebook")
        .expect(302)
        .expect((res) => {
          expect(res.headers.location).toContain("facebook");
        });
    });

    it("should redirect to LinkedIn OAuth", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/linkedin")
        .expect(302)
        .expect((res) => {
          expect(res.headers.location).toContain("linkedin");
        });
    });

    // Note: OAuth callback tests would require mocking the OAuth providers
    // which is beyond the scope of this basic test suite
    it("should have Google callback route", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/google/callback")
        .expect(302); // Should redirect even without proper OAuth flow
    });

    it("should have Facebook callback route", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/facebook/callback")
        .expect(302); // Should redirect even without proper OAuth flow
    });

    it("should have LinkedIn callback route", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/linkedin/callback")
        .expect(302); // Should redirect even without proper OAuth flow
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle malformed JSON in request body", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send("invalid-json")
        .set("Content-Type", "application/json")
        .expect(400);
    });

    it("should handle very long input strings", () => {
      const longString = "a".repeat(1000);
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({
          email: `${longString}@example.com`,
          password: "Password123!",
          firstName: longString,
          lastName: longString,
        })
        .expect(400);
    });

    it("should handle SQL injection attempts in email", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: "<EMAIL>'; DROP TABLE users; --",
          password: "Password123!",
        })
        .expect(400);
    });

    it("should handle XSS attempts in input", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({
          email: "<EMAIL>",
          password: "Password123!",
          firstName: "<script>alert('xss')</script>",
          lastName: "User",
        })
        .expect(400);
    });

    it("should handle requests with wrong content type", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send("email=<EMAIL>&password=Password123!")
        .set("Content-Type", "application/x-www-form-urlencoded")
        .expect(400);
    });

    it("should handle non-existent routes", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/nonexistent")
        .expect(404);
    });

    it("should handle unsupported HTTP methods", () => {
      return request(app.getHttpServer())
        .patch("/v1/auth/login")
        .expect(405);
    });
  });

  describe("Rate Limiting and Security", () => {
    it("should reject requests with excessive data", () => {
      const largeData = {
        email: "<EMAIL>",
        password: "Password123!",
        firstName: "Test",
        lastName: "User",
        extraData: "x".repeat(10000),
      };

      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send(largeData)
        .expect(400);
    });

    it("should handle concurrent requests gracefully", async () => {
      const requests = Array(5).fill(null).map((_, index) => {
        return request(app.getHttpServer())
          .post("/v1/auth/register")
          .send({
            email: `concurrent${index}@example.com`,
            password: "Password123!",
            firstName: "Concurrent",
            lastName: `User${index}`,
          });
      });

      const results = await Promise.allSettled(requests);
      
      // At least some requests should succeed
      const successfulRequests = results.filter(result => 
        result.status === "fulfilled" && result.value.status === 201
      );
      
      expect(successfulRequests.length).toBeGreaterThan(0);
    });
  });
});
