import { User } from "@entities/user.entity";
import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";

import { BaseFilterDto, FilterService, IFilterResult } from "@/common/filter";
import { UserRole } from "@/types";

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private filterService: FilterService,
  ) {}

  async findAll(): Promise<User[]> {
    return this.usersRepository.find();
  }

  /**
   * Find all users with filtering, search, pagination, and sorting
   */
  async findAllWithFilters(
    filters: BaseFilterDto,
  ): Promise<IFilterResult<User>> {
    const queryBuilder = this.usersRepository.createQueryBuilder("user");
    const { data, metadata } = await this.filterService.applyFilters<User>(
      queryBuilder,
      filters,
      {
        searchFields: ["user.email", "user.phoneNumber"],
        defaultSortField: "user.createdAt",
        allowedSortFields: [
          "user.email",
          "user.phoneNumber",
          "user.createdAt",
          "user.updatedAt",
          "user.id",
          "user.role",
        ],
        maxLimit: 100,
      },
    );
    return {
      data,
      metadata,
    };
  }

  async findOne(email: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { email } });
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { email } });
  }

  async findByPhone(phoneNumber: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { phoneNumber } });
  }

  async findById(id: number): Promise<User | null> {
    return this.usersRepository.findOne({ where: { id } });
  }

  async findByGoogleId(googleId: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { googleId } });
  }

  async findByFacebookId(facebookId: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { facebookId } });
  }

  async findByLinkedInId(linkedinId: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { linkedinId } });
  }

  async findByRole(role: UserRole): Promise<User[]> {
    return this.usersRepository.find({ where: { role } });
  }

  /**
   * Find users by role with filtering, search, pagination, and sorting
   */
  async findByRoleWithFilters(
    role: UserRole,
    filters: BaseFilterDto,
  ): Promise<IFilterResult<User>> {
    const queryBuilder = this.usersRepository
      .createQueryBuilder("user")
      .where("user.role = :role", { role });

    return await this.filterService.applyFilters(queryBuilder, filters, {
      searchFields: ["user.email", "user.phoneNumber"],
      defaultSortField: "user.createdAt",
      allowedSortFields: [
        "user.email",
        "user.phoneNumber",
        "user.createdAt",
        "user.updatedAt",
        "user.id",
        "user.role",
      ],
      maxLimit: 100,
    });
  }

  async findByIdWithProfile(id: number): Promise<User | null> {
    return this.usersRepository.findOne({
      where: { id },
      relations: ["profile"],
    });
  }

  async create(user: Partial<User>): Promise<User | null> {
    const newUser = this.usersRepository.create(user);
    return this.usersRepository.save(newUser);
  }

  async update(id: number, updateData: Partial<User>): Promise<User | null> {
    await this.usersRepository.update(id, updateData);
    return this.findById(id);
  }
}
