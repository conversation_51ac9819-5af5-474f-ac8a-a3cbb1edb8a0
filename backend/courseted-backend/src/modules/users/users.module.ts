import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";

import { FilterModule } from "@/common/filter";
import { Profile, User } from "@/entities";

import { ProfileService } from "../profile/services/profile.service";
import { UsersController } from "./controllers/users.controller";
import { UsersService } from "./services/users.service";

@Module({
  imports: [TypeOrmModule.forFeature([User, Profile]), FilterModule],
  controllers: [UsersController],
  providers: [UsersService, ProfileService],
  exports: [UsersService, ProfileService],
})
export class UsersModule {}
