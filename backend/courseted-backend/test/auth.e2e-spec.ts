import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication, ValidationPipe } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../src/app.module";
import { getRepositoryToken } from "@nestjs/typeorm";
import { User } from "../src/entities/user.entity";
import { Repository } from "typeorm";
import { JwtService } from "@nestjs/jwt";

describe("AuthController (e2e)", () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let jwtService: JwtService;

  // Test user data
  const testUser = {
    email: "<EMAIL>",
    password: "Password123!",
    firstName: "Test",
    lastName: "User",
  };

  // Setup before tests
  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true }));

    userRepository = moduleFixture.get<Repository<User>>(
      getRepositoryToken(User),
    );
    jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.init();

    // Clean up database before tests
    await userRepository.delete({});
  });

  afterAll(async () => {
    // Clean up after tests
    await userRepository.delete({});
    await app.close();
  });

  describe("Registration", () => {
    it("should register a new user", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send(testUser)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty("id");
          expect(res.body.email).toBe(testUser.email);
          expect(res.body.firstName).toBe(testUser.firstName);
          expect(res.body.lastName).toBe(testUser.lastName);
          expect(res.body).not.toHaveProperty("password");
        });
    });

    it("should not register a user with an existing email", async () => {
      // First create a user
      await userRepository.save({
        email: "<EMAIL>",
        password: "hashedpassword",
        firstName: "Existing",
        lastName: "User",
      });

      // Try to register with the same email
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({
          email: "<EMAIL>",
          password: "Password123!",
          firstName: "Another",
          lastName: "User",
        })
        .expect(401)
        .expect((res) => {
          expect(res.body.message).toContain("already exists");
        });
    });

    it("should validate registration input", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/register")
        .send({
          email: "invalid-email",
          password: "123", // Too short
        })
        .expect(400);
    });
  });

  describe("Login", () => {
    beforeEach(async () => {
      // Create a test user for login tests
      const hashedPassword = await require("bcrypt").hash("Password123!", 10);
      await userRepository.save({
        email: "<EMAIL>",
        password: hashedPassword,
        firstName: "Login",
        lastName: "User",
      });
    });

    it("should login with valid credentials", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: "<EMAIL>",
          password: "Password123!",
        })
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty("access_token");
          expect(res.body).toHaveProperty("user");
          expect(res.body.user.email).toBe("<EMAIL>");
        });
    });

    it("should not login with invalid credentials", () => {
      return request(app.getHttpServer())
        .post("/v1/auth/login")
        .send({
          email: "<EMAIL>",
          password: "WrongPassword",
        })
        .expect(401);
    });
  });

  describe("Profile", () => {
    let authToken: string;

    beforeEach(async () => {
      // Create a user and generate a token
      const user = await userRepository.save({
        email: "<EMAIL>",
        password: "hashedpassword",
        firstName: "Profile",
        lastName: "User",
      });

      authToken = jwtService.sign({
        email: user.email,
        sub: user.id,
      });
    });

    it("should get user profile with valid token", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/profile")
        .set("Authorization", `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.email).toBe("<EMAIL>");
        });
    });

    it("should not get profile without token", () => {
      return request(app.getHttpServer()).get("/v1/auth/profile").expect(401);
    });

    it("should not get profile with invalid token", () => {
      return request(app.getHttpServer())
        .get("/v1/auth/profile")
        .set("Authorization", "Bearer invalid-token")
        .expect(401);
    });
  });

  describe("OAuth Routes", () => {
    it("should have Google auth route", () => {
      return request(app.getHttpServer()).get("/v1/auth/google").expect(302);
    });

    it("should have Facebook auth route", () => {
      return request(app.getHttpServer()).get("/v1/auth/facebook").expect(302);
    });

    it("should have LinkedIn auth route", () => {
      return request(app.getHttpServer()).get("/v1/auth/linkedin").expect(302);
    });
  });
});
